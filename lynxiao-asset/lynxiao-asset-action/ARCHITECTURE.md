# 数据处理动作模块架构设计文档

## 1. 架构概述

### 系统定位
`lynxiao-asset-action`是凌霄数据资产平台的核心数据处理引擎，提供一套可组合、可独立部署的原子化数据处理动作（Actions）。系统通过消息队列驱动，构成数据处理流程中的基本执行单元，专注于各种数据转换、分析和处理任务。

**业务价值主张**：
- 原子化处理：每个Action专注单一数据处理任务
- 高度可组合：Actions可灵活组合成复杂处理流程
- 高性能处理：支持大规模数据的并行处理
- 智能化能力：集成AI技术提供智能数据处理

**目标用户群体**：
- 数据工程师：设计和优化数据处理流程
- AI工程师：开发和部署智能处理算法
- 业务分析师：配置数据处理规则和策略
- 系统架构师：规划数据处理架构和性能优化

### 架构原则
- **单一职责**：每个Action只负责一个特定的处理任务
- **无状态设计**：Actions不保存状态，支持水平扩展
- **事件驱动**：完全基于消息队列的异步处理模式
- **可配置性**：支持动态配置和功能开关#
# 2. 整体架构设计

### 架构风格
系统采用**微内核架构**结合**插件化设计**：
- **微内核**：核心框架提供基础能力，具体处理逻辑通过插件实现
- **事件驱动**：所有处理都由MQ消息触发
- **管道过滤器**：数据在不同Action间流转处理
- **策略模式**：不同类型的数据采用不同的处理策略

### 系统边界
- **功能边界**：专注于数据处理，不涉及调度和管理
- **技术边界**：基于Spring Boot和Pandora MQ
- **数据边界**：处理各种格式的结构化和非结构化数据
- **性能边界**：支持TB级数据的批量处理

### 架构视图

```mermaid
graph TB
    subgraph "消息驱动层"
        A[Pandora MQ] --> B[消息路由器]
    end

    subgraph "Action处理层"
        B --> C[bucket-read]
        B --> D[doc-parse]
        B --> E[doc-split]
        B --> F[index-calc]
        B --> G[index-sink]
        B --> H[其他Actions]
    end

    subgraph "数据访问层"
        C --> I[MongoDB Client]
        G --> I
        G --> J[Elasticsearch Client]
        F --> K[AI服务客户端]
    end

    subgraph "基础设施层"
        L[配置管理] --> C
        L --> D
        L --> E
        L --> F
        L --> G
        M[监控日志] --> C
        M --> D
        M --> E
        M --> F
        M --> G
    end

    style B fill:#f9f,stroke:#333,stroke-width:2px
    style C fill:#cfc,stroke:#333,stroke-width:2px
    style D fill:#ccf,stroke:#333,stroke-width:2px
    style F fill:#fec,stroke:#333,stroke-width:2px
```

## 3. 分层架构设计

### 消息驱动层（Message-Driven Layer）
**职责**：处理消息队列的消费和路由

**核心组件**：
- **消息消费者**：监听特定MQ主题的消息
- **消息路由器**：根据消息类型路由到对应的Action
- **消息序列化**：处理消息的序列化和反序列化
- **错误处理**：消息处理失败的重试和死信处理

### Action处理层（Action Processing Layer）
**职责**：实现具体的数据处理逻辑

**处理模式**：
- **同步处理**：直接处理并返回结果
- **异步处理**：处理后发送消息到下游
- **批量处理**：批量处理多条数据提升效率
- **流式处理**：大数据量的流式处理

**Action分类**：
- **数据桶操作**：bucket-read, bucket-write
- **文档处理**：doc-parse, doc-split, doc-filter, doc-rule
- **索引处理**：index-calc, index-sink
- **通用处理**：transform, dataapi

### 数据访问层（Data Access Layer）
**职责**：提供对各种数据存储的访问能力

**存储适配器**：
- **MongoDB适配器**：文档数据的读写操作
- **Elasticsearch适配器**：搜索数据的索引和查询
- **GridFS适配器**：大文件的存储和读取
- **缓存适配器**：Redis缓存的操作

### 基础设施层（Infrastructure Layer）
**职责**：提供通用的技术支撑能力

**核心组件**：
- **配置管理**：动态配置和功能开关
- **监控日志**：性能监控和日志记录
- **异常处理**：统一的异常处理和恢复机制
- **资源管理**：线程池和连接池管理

## 4. 模块架构设计

### 模块划分原则
- **功能域划分**：按数据处理功能域划分模块
- **技术栈分离**：不同技术栈的处理逻辑分离
- **可插拔设计**：每个模块都可以独立启用或禁用
- **接口标准化**：统一的Action接口和消息格式

### 核心模块分析

#### 数据桶操作模块（Bucket Operations）
**功能职责**：数据桶的读写操作
**核心Actions**：
- **bucket-read**：从MongoDB数据桶读取数据
  - 支持全量、增量、指定ID列表读取
  - 虚拟线程高并发扫描
  - 流式处理避免内存溢出
- **bucket-write**：向MongoDB数据桶写入数据
  - 批量upsert操作
  - 大字段分离存储
  - 事务保证数据一致性

#### 文档处理模块（Document Processing）
**功能职责**：各种文档数据的处理和转换
**核心Actions**：
- **doc-parse**：文件内容解析
  - 基于Apache Tika的多格式支持
  - GridFS文件读取
  - 文本内容提取
- **doc-split**：文档内容切分
  - 基于langchain4j的智能切分
  - 支持多种切分策略
  - 保持语义完整性
- **doc-filter**：文档过滤
  - 动态表达式引擎
  - 灵活的过滤条件
  - 高性能过滤处理
- **doc-rule**：文档规则处理
  - 静态规则和动态脚本
  - Groovy脚本执行
  - 字段映射和转换

#### 索引处理模块（Index Processing）
**功能职责**：搜索索引的计算和写入
**核心Actions**：
- **index-calc**：特征计算
  - 文本分词处理
  - 向量特征计算
  - 缓存机制优化
- **index-sink**：索引写入
  - Elasticsearch批量写入
  - MongoDB数据同步
  - 错误重试机制

#### 通用处理模块（Generic Processing）
**功能职责**：通用的数据处理能力
**核心Actions**：
- **transform**：通用数据转换
  - Groovy脚本执行环境
  - 灵活的数据转换逻辑
  - 动态脚本加载
- **doc-dataapi**：数据API增强
  - URL解析和站点识别
  - 医疗信息补全
  - 字段映射和默认值

## 5. 数据架构设计

### 数据模型设计
系统处理的数据模型以**AssetCell**为核心：

```mermaid
classDiagram
    class AssetCell {
        +String id
        +String bucketId
        +Map~String,Object~ fields
        +Timestamp createTime
        +Timestamp updateTime
        +String status
    }
    
    class ProcessingContext {
        +String taskId
        +String executionId
        +String batchId
        +Map~String,Object~ parameters
    }
    
    class ActionMessage {
        +String actionType
        +AssetCell data
        +ProcessingContext context
        +Map~String,Object~ config
    }
    
    AssetCell --> ProcessingContext
    ActionMessage --> AssetCell
    ActionMessage --> ProcessingContext
```

### 数据流转设计
数据在Actions间的流转遵循**管道过滤器模式**：

```mermaid
graph LR
    A[Source Bucket] --> B[bucket-read]
    B --> C[doc-parse]
    C --> D[doc-split]
    D --> E[index-calc]
    E --> F[index-sink]
    F --> G[Target Storage]
    
    H[MQ消息] --> B
    B --> I[MQ消息]
    I --> C
    C --> J[MQ消息]
    J --> D
    D --> K[MQ消息]
    K --> E
    E --> L[MQ消息]
    L --> F
```

### 数据处理策略
- **流式处理**：大数据量采用流式处理避免内存溢出
- **批量处理**：小数据量采用批量处理提升效率
- **并行处理**：利用虚拟线程实现高并发处理
- **容错处理**：异常数据的隔离和重试机制

## 6. 集成架构设计

### 内部集成
**Action间通信**：
- 完全通过MQ消息进行异步通信
- 标准化的消息格式和协议
- 支持消息的路由和广播

**配置集成**：
- 统一的配置管理机制
- 动态配置刷新能力
- 环境隔离配置

### 外部集成
**AI服务集成**：
- **分词服务**：文本分词和语言处理
- **向量服务**：文本向量化和相似度计算
- **解析服务**：文档内容解析和提取

**存储系统集成**：
- **MongoDB**：文档数据的存储和查询
- **Elasticsearch**：搜索索引的构建和查询
- **GridFS**：大文件的存储和访问
- **Redis**：缓存和临时数据存储

**监控系统集成**：
- **指标上报**：处理性能和业务指标
- **日志收集**：结构化日志和错误信息
- **告警通知**：异常情况的实时告警

## 7. 安全架构设计

### 数据安全
**数据传输安全**：
- MQ消息的加密传输
- 敏感数据的脱敏处理
- 数据完整性校验

**数据存储安全**：
- 大字段的分离存储
- 敏感信息的加密存储
- 访问权限控制

### 处理安全
**脚本执行安全**：
- Groovy脚本的沙箱执行
- 资源使用限制
- 恶意代码检测

**服务调用安全**：
- 外部服务的认证和授权
- API调用的频率限制
- 服务降级和熔断

## 8. 性能架构设计

### 性能目标
- **处理吞吐量**：10,000+ 条/秒
- **处理延迟**：平均 < 100ms
- **并发处理**：支持1000+并发Action
- **资源利用率**：CPU利用率 < 80%

### 性能优化策略
**并发优化**：
- 虚拟线程提升并发能力
- 异步处理减少阻塞
- 批量操作提升效率

**内存优化**：
- 流式处理避免内存溢出
- 对象池复用减少GC压力
- 大字段分离存储

**IO优化**：
- 连接池复用数据库连接
- 批量读写减少IO次数
- 缓存机制减少重复计算

### 扩展性设计
**水平扩展**：
- 无状态设计支持多实例部署
- 消息队列天然支持负载分发
- 动态扩缩容能力

**垂直扩展**：
- 多线程处理提升单机性能
- 资源配置优化
- JVM参数调优

## 9. 部署架构设计

### 部署模式
**单体部署**：
- 所有Actions部署在同一应用中
- 通过配置开关控制功能
- 适用于小规模场景

**微服务部署**：
- 不同类型的Actions独立部署
- 专门的资源配置和优化
- 适用于大规模生产环境

### 基础设施
**容器化部署**：
- Docker镜像打包
- Kubernetes编排
- 自动扩缩容

**依赖服务**：
- Pandora MQ消息队列
- MongoDB数据库集群
- Elasticsearch搜索集群
- Redis缓存集群

### 发布策略
**灰度发布**：
- 按Action类型分批发布
- 流量逐步切换
- 快速回滚机制

**蓝绿部署**：
- 零停机更新
- 完整环境验证
- 一键切换

## 10. 技术选型说明

### 框架选择
**Spring Boot 3.4.x**：
- 选择理由：成熟稳定，生态丰富
- 配置优化：生产环境性能调优
- 扩展能力：支持自定义Action开发

**Kafka MQ**：
- 选择理由：消息队列，深度集成
- 性能特点：高吞吐量，低延迟
- 可靠性：消息持久化和重试机制

### AI技术选择
**Langchain4j**：
- 选择理由：Java生态的AI框架，功能丰富
- 集成能力：支持多种AI模型和服务
- 扩展性：易于集成新的AI能力

**Apache Tika**：
- 选择理由：成熟的文档解析库，格式支持全面
- 性能特点：高效的文档内容提取
- 稳定性：经过大规模生产验证

### 存储技术选择
**MongoDB**：
- 选择理由：灵活的文档存储，支持复杂查询
- 性能特点：高并发读写，水平扩展
- 数据模型：适合非结构化数据存储

**Elasticsearch**：
- 选择理由：强大的全文搜索和分析能力
- 性能特点：近实时搜索，高可用
- 生态系统：丰富的插件和工具
