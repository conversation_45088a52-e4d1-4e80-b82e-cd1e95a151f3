package com.iflytek.lynxiao.asset.action.index.sink;

import com.alibaba.fastjson2.JSONArray;
import com.google.common.util.concurrent.RateLimiter;
import com.iflytek.lynxiao.asset.action.BaseHandler;
import com.iflytek.lynxiao.asset.action.index.sink.config.IndexSinkProperties;
import com.iflytek.lynxiao.asset.action.index.sink.domain.ProcessResult;
import com.iflytek.lynxiao.asset.action.index.sink.service.MongoService;
import com.iflytek.lynxiao.asset.api.EsServiceApi;
import com.iflytek.lynxiao.common.config.LynxiaoProperties;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.data.domain.*;
import com.iflytek.lynxiao.data.dto.action.sink.IndexDbWriteInputDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import skynet.boot.exception.SkynetErrorCode;
import skynet.boot.pandora.api.ApiRequest;
import skynet.boot.pandora.api.ApiResponse;
import skynet.boot.pandora.exception.PandoraException;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @author: leitong
 * @date: 2025/6/5 13:50
 * @description: 索引库写入流程处理器
 **/
@Slf4j
public class IndexSinkMqHandler extends BaseHandler {

    private final MongoService mongoService;
    private final EsServiceApi esServiceApi;
    private final LynxiaoProperties lynxiaoProperties;
    private final IndexSinkProperties indexSinkProperties;
    private final RateLimiter rateLimiter;

    public IndexSinkMqHandler(MongoService mongoService,
                              EsServiceApi esServiceApi,
                              LynxiaoProperties lynxiaoProperties,
                              IndexSinkProperties sinkProperties) {
        this.mongoService = mongoService;
        this.esServiceApi = esServiceApi;
        this.lynxiaoProperties = lynxiaoProperties;
        this.indexSinkProperties = sinkProperties;
        this.rateLimiter = sinkProperties.getSyncQps() > 0 ? RateLimiter.create(sinkProperties.getSyncQps()) : null;
    }

    @Override
    public ApiResponse process(ApiRequest apiRequest) throws PandoraException {
        String traceId = apiRequest.getTraceId();
        try {
            if (Objects.nonNull(rateLimiter)) {
                this.rateLimiter.acquire();
            }
            IndexDbWriteInputDTO input = apiRequest.getPayload().to(IndexDbWriteInputDTO.class);

            //数据预处理
            preProcess(input.getData(), input.getAuditType());

            // 操作分离
            InputSeparateResult separateResult = separateDeleteData(input);
            IndexDbWriteInputDTO upsert = separateResult.getUpsert();
            IndexDbWriteInputDTO delete = separateResult.getDelete();

            // 根据区域编码判断处理逻辑
            if (indexSinkProperties.getCenterCode().contains(lynxiaoProperties.getRegionCode())) {
                // 合肥区域：mongo upsert完整输入；es upsert 和 delete 分离结果
                upsertMongoData(traceId, input);
                deleteEsData(traceId, delete);
                upsertEsData(traceId, upsert);

            } else {
                // 非合肥验证环境区域：mongo和es 均 upsert 和 delete 分离结果，这里需要注意es和mongo的操作顺序
                upsertMongoData(traceId, upsert);
                upsertEsData(traceId, upsert);
                deleteEsData(traceId, delete);
                deleteMongoData(traceId, delete);
            }
        } catch (PandoraException pe) {
            throw pe;
        } catch (LynxiaoException le) {
            log.error("traceId[{}] error[{}] in SinkMqHandler: {}", traceId, le.getCode(), le.getMessage());
            log.debug("traceId[{}]", traceId, le);
            throw toPandoraException(le);
        } catch (Exception e) {
            log.error("traceId[{}] exception in SinkMqHandler", traceId, e);
            throw new PandoraException("exception in SinkMqHandler: " + e.getMessage(), e);
        }

        return new ApiResponse();
    }


    private void preProcess(List<AssetCell> cellList, Integer auditType) {
        for (AssetCell cell : cellList) {
            checkId(cell);
            forceVector2Float(cell);
            setAuditStatus(cell, auditType);
        }
    }

    private void checkId(AssetCell cell) {
        if (cell.getIdOptional().isEmpty()) {
            throw new LynxiaoException(SkynetErrorCode.PARAM_INVALID.getCode(), "_id is required");
        }
    }

    // 强制转换向量为Float类型，防止kafka中传输时变为Double类型
    private void forceVector2Float(AssetCell cell) {
        JSONArray embeddings = cell.getJSONArray(AssetCell.EMBEDDINGS);
        for (int i = 0; i < embeddings.size(); i++) {
            List<Float> vector = embeddings.getJSONObject(i).getList(AssetCell.EMBEDDING, Float.class);
            embeddings.getJSONObject(i).put(AssetCell.EMBEDDING, vector);
        }
    }

    private PandoraException toPandoraException(LynxiaoException e) {
        return new PandoraException(e.getCode(), e.getMessage());
    }

    private void setAuditStatus(AssetCell assetCell, Integer auditType) {
        // 刷新桶状态
        AssetCellProps props = assetCell.getProps();
        props.setUts(System.currentTimeMillis());
        // 索引库同步数据不需要刷新审核状态（hf --> sh）
        if (auditType != null) {
            props.setAuditStatus(AssetAuditStatus.of(auditType), new AssetAuditor());
        }
    }


    private void upsertEsData(String traceId, IndexDbWriteInputDTO input) throws LynxiaoException, IOException {
        ProcessResult result = esServiceApi.process(traceId, input);
        if (!result.isSuccess()) {
            throw new LynxiaoException(SkynetErrorCode.ERROR.getCode(), "ES更新失败: " + result.getErrorMessage());
        }
    }

    private void deleteEsData(String traceId, IndexDbWriteInputDTO input) throws LynxiaoException, IOException {
        ProcessResult result = esServiceApi.processDelete(traceId, input);
        if (!result.isSuccess()) {
            throw new LynxiaoException(SkynetErrorCode.ERROR.getCode(), "ES删除失败: " + result.getErrorMessage());
        }
    }

    private void upsertMongoData(String traceId, IndexDbWriteInputDTO input) throws LynxiaoException {
        ProcessResult result = mongoService.process(traceId, input);
        if (!result.isSuccess()) {
            throw new LynxiaoException(SkynetErrorCode.ERROR.getCode(), "MongoDB更新失败: " + result.getErrorMessage());
        }
    }

    private void deleteMongoData(String traceId, IndexDbWriteInputDTO input) throws LynxiaoException {
        ProcessResult result = mongoService.processDelete(traceId, input);
        if (!result.isSuccess()) {
            throw new LynxiaoException(SkynetErrorCode.ERROR.getCode(), "MongoDB删除失败: " + result.getErrorMessage());
        }
    }


    /**
     * 分离删除数据
     * 修改原input为只包含非删除数据，返回包含删除数据的新input
     *
     * @param input 原始输入，会被修改为只包含非删除数据
     * @return 包含删除数据的新input，如果没有删除数据则返回null
     */
    private InputSeparateResult separateDeleteData(IndexDbWriteInputDTO input) {
        List<AssetCell> deleteData = new ArrayList<>();
        List<AssetCell> upsertData = new ArrayList<>();

        // 分离删除数据和正常数据
        for (AssetCell cell : input.getData()) {
            if (shouldDelete(cell)) {
                deleteData.add(cell);
            } else {
                upsertData.add(cell);
            }
        }

        return new InputSeparateResult(deleteData, upsertData, input);
    }


    /**
     * 判断是否需要删除
     * 条件：_x.op=3（DELETE操作）且 s=1（去重标记为TRUE）
     */
    private boolean shouldDelete(AssetCell cell) {
        AssetCellProps props = cell.getProps();
        if (props == null) {
            return false;
        }

        // 检查操作类型是否为删除（op=3）
        AssetOperationType operationType = props.getOperationType();
        if (operationType == AssetOperationType.DELETE) {
            return true;
        }

        // 检查去重标记是否为1（s=1）
        Integer dupFlag = props.getInteger(AssetCellProps.S);
        return dupFlag != null && dupFlag == AssetCellProps.BoolValue.TRUE.getValue();
    }


    @Getter
    @Setter
    static class InputSeparateResult {
        private IndexDbWriteInputDTO delete;
        private IndexDbWriteInputDTO upsert;

        public InputSeparateResult(List<AssetCell> deleteData, List<AssetCell> upsertData, IndexDbWriteInputDTO original) {
            this.delete = copyInput(original, deleteData);
            this.upsert = copyInput(original, upsertData);
        }

        private IndexDbWriteInputDTO copyInput(IndexDbWriteInputDTO source, List<AssetCell> cellList) {
            IndexDbWriteInputDTO copy = new IndexDbWriteInputDTO();
            BeanUtils.copyProperties(source, copy);
            copy.setData(cellList);
            return copy;
        }
    }
}