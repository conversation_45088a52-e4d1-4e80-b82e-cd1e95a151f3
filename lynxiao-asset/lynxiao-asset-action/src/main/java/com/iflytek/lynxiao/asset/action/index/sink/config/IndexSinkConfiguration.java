package com.iflytek.lynxiao.asset.action.index.sink.config;

import com.iflytek.lynxiao.asset.action.index.sink.IndexSinkMqHandler;
import com.iflytek.lynxiao.asset.action.index.sink.service.MongoService;
import com.iflytek.lynxiao.asset.api.EsServiceApi;
import com.iflytek.lynxiao.asset.api.FeatureBaseApi;
import com.iflytek.lynxiao.asset.cache.BucketCacheService;
import com.iflytek.lynxiao.asset.config.ActionProperties;
import com.iflytek.lynxiao.common.config.LynxiaoProperties;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.data.mongodb.core.MongoTemplate;
import skynet.boot.pandora.ogma.annotation.EnableSkynetPandoraOgma;
import skynet.boot.pandora.ogma.feign.OgmaFeignClientBuilder;

/**
 * @author: leitong
 * @date: 2025/6/5 11:36
 * @description:
 **/
@EnableSkynetPandoraOgma
@EnableFeignClients
@Configuration(proxyBeanMethods = false)
@ImportAutoConfiguration(exclude = DataSourceAutoConfiguration.class)
@Import({
        org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration.class,
        org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration.class
})
@ConditionalOnProperty(value = "lynxiao.asset.action.index.sink.enabled")
public class IndexSinkConfiguration {


    @Bean
    @ConfigurationProperties(prefix = "lynxiao.asset.action.index.sink")
    public IndexSinkProperties indexSinkProperties() {
        return new IndexSinkProperties();
    }


    @Bean
    public FeatureBaseApi featureBaseApi(OgmaFeignClientBuilder ogmaFeignClientBuilder, ActionProperties properties) {
        return new FeatureBaseApi(ogmaFeignClientBuilder, properties);
    }


    @Bean
    public MongoService mongoService(FeatureBaseApi featureBaseApi,
                                     @Qualifier(value = "featureMongoTemplate") MongoTemplate mongoTemplate,
                                     IndexSinkProperties properties,
                                     MeterRegistry meterRegistry,
                                     LynxiaoProperties lynxiaoProperties) {
        return new MongoService(featureBaseApi, mongoTemplate, properties, meterRegistry, lynxiaoProperties);
    }

    @Bean
    public EsServiceApi esServiceApi(FeatureBaseApi featureBaseApi,
                                     ActionProperties properties,
                                     MeterRegistry meterRegistry) {
        return new EsServiceApi(properties, featureBaseApi, meterRegistry);
    }

    @Bean
    public IndexSinkMqHandler sinkMqHandler(MongoService mongoService,
                                            EsServiceApi esServiceApi,
                                            LynxiaoProperties lynxiaoProperties,
                                            IndexSinkProperties indexSinkProperties) {
        return new IndexSinkMqHandler(mongoService, esServiceApi, lynxiaoProperties, indexSinkProperties);
    }

}