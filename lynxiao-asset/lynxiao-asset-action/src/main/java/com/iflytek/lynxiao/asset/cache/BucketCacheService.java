package com.iflytek.lynxiao.asset.cache;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.iflytek.lynxiao.asset.config.ActionProperties;
import com.iflytek.lynxiao.common.cache.AssetPortalCacheService;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.data.dto.asset.AssetBucketStorageDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * 缓存桶连接
 *
 * @author: cwruan
 * @Date: 2025-05-29 17:12
 */
@Slf4j
public class BucketCacheService {
    private final MongoClientCacheService mongoClientCacheService;
    private final AssetPortalCacheService assetPortalCacheService;
    private final Cache<String, AssetBucketStorageDTO> bucketCache;

    public BucketCacheService(MongoClientCacheService mongoClientCacheService, ActionProperties actionProperties,
                              AssetPortalCacheService assetPortalCacheService) {
        this.mongoClientCacheService = mongoClientCacheService;
        this.assetPortalCacheService = assetPortalCacheService;
        this.bucketCache = Caffeine.newBuilder().expireAfterWrite(actionProperties.getCacheTtl()).build();
    }

    /**
     * 获取bucket对应mongo
     *
     * @param bucketCode 桶编码
     * @return mongo连接
     */
    public CacheMongoTemplate getClient(String bucketCode) {
        // 根据code获取对应的mongo地址,若无缓存则请求平台
        AssetBucketStorageDTO bucketStorageDTO = this.getBucket(bucketCode);
        return mongoClientCacheService.getClient(bucketStorageDTO.getMongoCluster());
    }

    /**
     * 获取bucket信息 懒加载
     *
     * @param bucketCode 桶编码
     * @return mongo连接
     */
    public AssetBucketStorageDTO getBucket(String bucketCode) {
        AssetBucketStorageDTO assetBucketStorageDTO = this.bucketCache.get(bucketCode, k -> assetPortalCacheService.getBucketStorage(bucketCode));
        if (assetBucketStorageDTO == null) {
            log.error("get bucket from redis failed! bucketCode: {}", bucketCode);
            throw new LynxiaoException(bucketCode + "get from redis failed!");
        }
        return assetBucketStorageDTO;
    }
}