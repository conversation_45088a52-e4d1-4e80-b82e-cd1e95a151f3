package com.iflytek.lynxiao.asset.action.index.sink.service;

import com.iflytek.lynxiao.asset.action.index.sink.config.IndexSinkProperties;
import com.iflytek.lynxiao.asset.action.index.sink.domain.ProcessResult;
import com.iflytek.lynxiao.asset.api.FeatureBaseApi;
import com.iflytek.lynxiao.common.config.LynxiaoProperties;
import com.iflytek.lynxiao.data.domain.AssetCell;
import com.iflytek.lynxiao.data.dto.action.sink.IndexDbWriteInputDTO;
import com.iflytek.lynxiao.data.dto.asset.field.FieldItem;
import com.iflytek.lynxiao.data.dto.featurebase.FeatureBaseDetailGetDTO;
import com.mongodb.bulk.BulkWriteInsert;
import com.mongodb.bulk.BulkWriteResult;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Tags;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.bson.BsonInt64;
import org.bson.BsonValue;
import org.bson.Document;
import org.springframework.data.mongodb.BulkOperationException;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.CollectionUtils;
import skynet.boot.exception.SkynetException;
import skynet.boot.mongo.DocumentCompressor;
import skynet.boot.pandora.brave.annotation.PandoraMetric;
import skynet.boot.pandora.brave.metric.PandoraMetricHolder;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * MongoDB数据同步服务实现类
 * 负责处理索引数据到MongoDB的同步操作，支持数据压缩和批量写入
 */
@Slf4j
public class MongoService {

    private final FeatureBaseApi featureBaseService;    // 特征库服务
    private final MongoTemplate mongoTemplate;             // MongoDB操作模板
    private final DocumentCompressor documentCompressor;    // 文档压缩器
    private final IndexSinkProperties properties;          // 同步配置属性
    private final MeterRegistry meterRegistry;             // 指标注册器
    private final LynxiaoProperties lynxiaoProperties;

    /**
     * 构造函数
     *
     * @param featureBaseService 特征库服务
     * @param mongoTemplate      MongoDB操作模板（使用特定的featureMongoTemplate）
     * @param properties         同步配置属性
     * @param meterRegistry      指标注册器
     */
    public MongoService(FeatureBaseApi featureBaseService, MongoTemplate mongoTemplate, IndexSinkProperties properties,
                        MeterRegistry meterRegistry, LynxiaoProperties lynxiaoProperties) {
        this.featureBaseService = featureBaseService;
        this.mongoTemplate = mongoTemplate;
        this.documentCompressor = new DocumentCompressor(properties.getCompressThreshold());
        this.properties = properties;
        this.lynxiaoProperties = lynxiaoProperties;
        this.meterRegistry = meterRegistry;
    }

    /**
     * 处理索引数据同步
     * 根据操作类型执行相应的MongoDB操作（新增、更新、删除）
     *
     * @return 处理结果
     */
    @PandoraMetric(value = "sink.mongo.write")
    public ProcessResult process(String traceId, IndexDbWriteInputDTO input) {
        return executeOperation(traceId, input, "upsert", this::bulkUpsert);
    }

    /**
     * 处理删除操作
     */
    @PandoraMetric(value = "sink.mongo.delete")
    public ProcessResult processDelete(String traceId, IndexDbWriteInputDTO input) {
        return executeOperation(traceId, input, "delete", this::bulkDelete);
    }

    /**
     * 通用的操作执行方法，消除process和processDelete的重复代码
     *
     * @param traceId       跟踪ID
     * @param input         输入数据
     * @param operationType 操作类型（用于日志和指标）
     * @param operation     具体的操作逻辑
     * @return 处理结果
     */
    private ProcessResult executeOperation(String traceId, IndexDbWriteInputDTO input, String operationType,
                                           Consumer<OperateParam> operation) {
        if (CollectionUtils.isEmpty(input.getData())) {
            return new ProcessResult();
        }

        ProcessResult result = null;
        try {
            // 获取特征库信息
            FeatureBaseDetailGetDTO featureBase = featureBaseService.getFeatureBase(traceId, input.getBucketCode());
            String col = CollectionUtils.lastElement(featureBase.getMongoFullCollections());

            // 执行具体操作
            OperateParam operateParam = OperateParam.builder().col(col).cells(input.getData()).fieldSpec(input.getFieldSpec()).build();
            operation.accept(operateParam);
            result = ProcessResult.success(input.getData());
        } catch (SkynetException e) {
            log.error("fail to {} mongodb, traceId[{}]，error code={} {}", operationType, traceId, e.getCode(), e.getMessage());
            result = ProcessResult.error(e, input.getData());
        } catch (Exception e) {
            log.error("fail to {} mongodb, traceId[{}]", operationType, traceId, e);
            result = ProcessResult.error(e, input.getData());
        } finally {
            // 记录操作指标
            String successTag = String.valueOf(Objects.requireNonNull(result).isSuccess());
            Tags tags = Tags.of(Tag.of("op", operationType), Tag.of("success", successTag));
            PandoraMetricHolder.setTags(tags);
            meterRegistry.counter(String.format("lynxiao.sink.mongo.%s.total", operationType), tags).increment(input.getData().size());
        }
        return result;
    }

    /**
     * 执行批量更新或插入操作
     * 采用两阶段策略：先尝试批量插入，失败后再执行更新
     *
     * @param operateParam 操作需参
     */
    private void bulkUpsert(OperateParam operateParam) {
        String collection = operateParam.getCol();
        List<AssetCell> documents = operateParam.getCells();
        // 校验字段, 非中心区域无需校验
        if (properties.getCenterCode().contains(lynxiaoProperties.getRegionCode())) {
            for (AssetCell document : documents) {
                document.validateFieldSpec(operateParam.getFieldSpec());
            }
        }

        // 根据配置决定是否压缩文档，减少存储空间占用
        List<AssetCell> compressedDocs = properties.getCompress() ?
                documents.stream().map(assetCell -> AssetCell.from(documentCompressor.compress(assetCell))).toList() : documents;

        //改成 Document 进行 insert
        List<Document> documentList = new ArrayList<>(compressedDocs.size());
        compressedDocs.forEach(document -> documentList.add(new Document(new TreeMap<>(document))));

        try {
            // 首先尝试批量插入
            BulkOperations bulkOps = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, collection);
            documentList.forEach(bulkOps::insert);
            BulkWriteResult result = bulkOps.execute();

            // 如果有文档未能成功插入，执行更新操作
            if (result.getInsertedCount() != documentList.size()) {
                List<AssetCell> upsertDocs = this.parseUpsertDocs(compressedDocs, result);
                this.doBulkUpsert(collection, upsertDocs);
            }
        } catch (BulkOperationException bulkOperationException) {
            // 处理批量操作异常，对未成功插入的文档执行更新
            BulkWriteResult result = bulkOperationException.getResult();
            List<AssetCell> upsertDocs = this.parseUpsertDocs(compressedDocs, result);
            this.doBulkUpsert(collection, upsertDocs);
        }
    }

    /**
     * 解析需要执行更新操作的文档
     * 通过比对批量插入结果，找出未成功插入的文档
     */
    private List<AssetCell> parseUpsertDocs(List<AssetCell> cellList, BulkWriteResult result) {
        List<BulkWriteInsert> inserts = result.getInserts();
        Set<Long> insertIds = inserts.stream().map(BulkWriteInsert::getId).map(BsonValue::asInt64).map(BsonInt64::getValue)
                .collect(Collectors.toSet());
        return cellList.stream().filter(doc -> !insertIds.contains(doc.getId())).collect(Collectors.toList());
    }

    /**
     * 执行批量更新操作
     * 使用MongoDB的upsert操作确保文档存在时更新，不存在时插入
     */
    private void doBulkUpsert(String collection, List<AssetCell> cellList) {
        BulkOperations bulkOps = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, collection);
        cellList.forEach(doc -> {
            Query query = Query.query(Criteria.where(AssetCell.ID).is(doc.getId()));
            Update update = new Update();
            doc.forEach((k, v) -> {
                if (!AssetCell.ID.equals(k)) {
                    update.set(k, v);
                }
            });
            bulkOps.upsert(query, update);
        });
        BulkWriteResult result = bulkOps.execute();
        log.debug("Bulk upsert completed: {} documents modified", result.getModifiedCount());
    }

    /**
     * 执行批量删除操作
     * 根据文档ID删除对应记录
     */
    private void bulkDelete(OperateParam operateParam) {
        BulkOperations bulkOps = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, operateParam.getCol());
        operateParam.getCells().forEach(doc -> {
            Query query = new Query(Criteria.where(AssetCell.ID).is(doc.getId()));
            bulkOps.remove(query);
        });
        BulkWriteResult result = bulkOps.execute();
        log.debug("Bulk delete completed: {} documents deleted", result.getDeletedCount());
    }

    @Getter
    @Setter
    @Builder
    private static class OperateParam {
        private String col;

        private List<AssetCell> cells;

        private List<FieldItem> fieldSpec;
    }

}