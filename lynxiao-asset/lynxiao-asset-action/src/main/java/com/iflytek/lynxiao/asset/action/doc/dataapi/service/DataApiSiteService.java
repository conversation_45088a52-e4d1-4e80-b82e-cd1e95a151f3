package com.iflytek.lynxiao.asset.action.doc.dataapi.service;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.iflytek.lynxiao.asset.action.doc.dataapi.config.DataApiDocProperties;
import com.iflytek.lynxiao.common.dataapi.DataApiRedisService;
import com.iflytek.lynxiao.data.dto.meta.MetaSiteRuleDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2025/06/16
 */
@Slf4j
public class DataApiSiteService {
    private final DataApiRedisService dataApiRedisService;
    private final DataApiDocProperties properties;

    /**
     * 站点信息缓存，避免频繁调用接口
     */
    private final Cache<String, String> siteCache;

    public DataApiSiteService(DataApiRedisService dataApiRedisService, DataApiDocProperties properties) {
        this.dataApiRedisService = dataApiRedisService;
        this.properties = properties;
        // 构建缓存
        this.siteCache = CacheBuilder.newBuilder()
                .maximumSize(properties.getMaxCacheSize())
                .expireAfterWrite(properties.getExpireAfterWrite(), TimeUnit.MINUTES)
                .recordStats()
                .build();
    }

    /**
     * 查找站点信息（带缓存）
     *
     * @param domain 域名
     * @return 站点信息
     */
    public String findSite(String domain) {
        if (StringUtils.isBlank(domain)) {
            throw new IllegalArgumentException("domain is blank!");
        }
        
        try {
            // 使用Cache.get(key, Callable)模式
            return siteCache.get(domain, () -> {
                log.debug("Cache miss for domain: {}, fetching from API", domain);
                return fetchSiteFromCache(domain);
            });
        } catch (Exception e) {
            log.error("Failed to get site info for domain: {}", domain, e);
            throw new RuntimeException("Failed to get site info for domain: " + domain, e);
        }
    }

    /**
     * 从API获取站点信息（实际的接口调用）
     *
     * @param domain 域名
     * @return 站点信息
     */
    private String fetchSiteFromCache(String domain) {
        // 查询缓存
        String cacheValue = dataApiRedisService.getDomainCache(domain);
        if (StringUtils.isNotBlank(cacheValue)) {
            log.debug("Cache find for domain: {}, site: {}", domain, cacheValue);
            return cacheValue;
        }

        // 遍历site映射表
        List<MetaSiteRuleDTO> ruleList = dataApiRedisService.getAllRules();
        log.debug("total rule size: {}", ruleList.size());
        for (MetaSiteRuleDTO rule : ruleList) {
            String[] array = rule.getRules().split(",");
            for (String r : array) {
                Pattern pattern = Pattern.compile(r);
                Matcher matcher = pattern.matcher(domain);
                if (matcher.find()) {
                    // domain => site 缓存
                    log.info("rule matched, domain:{}, site: {}", domain, rule.getSite());
                    dataApiRedisService.setDomainCache(domain, rule.getSite(),
                            properties.getRedisCacheExpire().toMinutes());
                    return rule.getSite();
                }
            }
        }
        log.info("no rule matched, domain:{}", domain);
        return "";
    }

    /**
     * 清除指定域名的缓存
     *
     * @param domain 域名
     */
    public void evictCache(String domain) {
        siteCache.invalidate(domain);
        log.info("Evicted cache for domain: {}", domain);
    }

    /**
     * 清除所有缓存
     */
    public void evictAllCache() {
        siteCache.invalidateAll();
        log.info("Evicted all cache");
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    public String getCacheStats() {
        return siteCache.stats().toString();
    }
}
