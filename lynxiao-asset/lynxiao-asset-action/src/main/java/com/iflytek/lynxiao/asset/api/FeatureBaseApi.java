package com.iflytek.lynxiao.asset.api;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.iflytek.lynxiao.asset.action.index.sink.exception.FeatureBaseException;
import com.iflytek.lynxiao.asset.config.ActionProperties;
import com.iflytek.lynxiao.common.feign.featurebase.FeatureBaseFeign;
import com.iflytek.lynxiao.data.dto.featurebase.FeatureBaseDetailGetDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import skynet.boot.pandora.api.ApiResponseGenerics;
import skynet.boot.pandora.ogma.feign.OgmaFeignClientBuilder;

import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
public class FeatureBaseApi {

    // 用异常缓存避免频繁调用FeatureBaseFeign接口
    private final Cache<String, RuntimeException> exceptionCache;

    private final OgmaFeignClientBuilder ogmaFeignClientBuilder;
    private final ActionProperties properties;

    public FeatureBaseApi(OgmaFeignClientBuilder ogmaFeignClientBuilder, ActionProperties properties) {
        this.ogmaFeignClientBuilder = ogmaFeignClientBuilder;
        this.properties = properties;
        if (this.properties.getExceptionCacheExpireTime() > 0) {
            this.exceptionCache = CacheBuilder.newBuilder().expireAfterWrite(properties.getExceptionCacheExpireTime(), TimeUnit.SECONDS)
                    .build();
        } else {
            this.exceptionCache = null;
        }

    }

    @Cacheable(cacheNames = "featureBaseInfo", key = "#p1")
    public FeatureBaseDetailGetDTO getFeatureBase(String traceId, String code) {
        RuntimeException cachedEx = Optional.ofNullable(this.exceptionCache).map(cache -> cache.getIfPresent(code)).orElse(null);
        if (Objects.nonNull(cachedEx)) {
            log.info("use exception from cache to avoid frequent call, code: {}", code);
            throw cachedEx;
        }
        FeatureBaseDetailGetDTO ret;
        log.debug("getFeatureBase traceId: {}, code: {}", traceId, code);
        try {
            FeatureBaseFeign featureApi = ogmaFeignClientBuilder.build(FeatureBaseFeign.class,
                    properties.getFeatureBaseName());
            // 调用远程接口获取索引配置
            ApiResponseGenerics<FeatureBaseDetailGetDTO> response = featureApi.getFeatureBaseDetailByCode(code);
            if (!ok(response)) {
                String errorMsg = String.format("FeatureBase服务调用失败[%s]: %s", traceId, response.getHeader().getMessage());
                log.error(errorMsg);
                throw new FeatureBaseException(errorMsg);
            }
            ret = response.getPayload();
            if (ret == null) {
                throw new FeatureBaseException("FeatureBase服务返回数据为空");
            }
            log.debug("getFeatureBase: {}", ret);
        } catch (Exception e) {
            RuntimeException runtimeException = (RuntimeException) e;
            if (Objects.nonNull(exceptionCache)) {
                exceptionCache.put(code, runtimeException);
            }
            throw e;
        }
        return ret;
    }

    private static boolean ok(ApiResponseGenerics<FeatureBaseDetailGetDTO> response) {
        return Objects.nonNull(response.getHeader()) &&
                response.getHeader().getCode() == 0 &&
                Objects.nonNull(response.getPayload());
    }
}
