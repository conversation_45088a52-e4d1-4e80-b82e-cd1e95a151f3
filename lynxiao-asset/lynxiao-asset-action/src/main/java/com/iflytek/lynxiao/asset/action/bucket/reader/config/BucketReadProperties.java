package com.iflytek.lynxiao.asset.action.bucket.reader.config;

import lombok.Getter;
import lombok.Setter;
import skynet.boot.pandora.api.Jsonable;

/**
 * 数据扫描配置
 *
 * @author: cwruan
 * @Date: 2025-05-29 20:32
 */
@Getter
@Setter
public class BucketReadProperties extends Jsonable {

    private boolean enabled;

    // 默认批次数量
    private int defaultBatch = 10;

    // 重试每页查询数量
    private int retryPageSize = 100;

    // 异步管道队列大小
    private int pipelineHandlerSize = 1000;

    // 汇报总数ack
    private String ackUrl = "mq://lynxiao_asset_task_ctl";

    // 任务延时时间 默认值5000ms
    private Long taskDelay = 5000L;

    // 读取每隔多少条打印一次日志
    private Long delayLog = 10000L;

    /**
     * 线程池配置
     */
    private ThreadPoolConfig threadPoolConfig;

    /**
     * 异步发送线程池配置
     */
    private AsyncSendThreadPoolConfig asyncSendThreadPoolConfig = new AsyncSendThreadPoolConfig();


    @Getter
    @Setter
    public static class ThreadPoolConfig {
        private int corePoolSize;
        private int maxPoolSize;
        private int queueCapacity;
    }

    @Getter
    @Setter
    public static class AsyncSendThreadPoolConfig {
        // 核心线程数，默认为主线程池的一半
        private int corePoolSize = 50;
        // 最大线程数，默认为主线程池的一半
        private int maxPoolSize = 50;
        // 队列容量
        private int queueCapacity = 1000;
        // 线程空闲时间（秒）
        private long keepAliveSeconds = 60;
    }

}