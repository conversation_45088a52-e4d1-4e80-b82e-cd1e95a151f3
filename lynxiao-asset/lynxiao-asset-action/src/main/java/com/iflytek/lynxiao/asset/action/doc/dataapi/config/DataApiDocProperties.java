package com.iflytek.lynxiao.asset.action.doc.dataapi.config;

import lombok.Getter;
import lombok.Setter;

import java.time.Duration;

/**
 * @author: leitong
 * @date: 2025/6/13 17:29
 * @description:
 **/
@Getter
@Setter
public class DataApiDocProperties {

    private Integer maxCacheSize = 20000;

    private Integer expireAfterWrite = 60;

    private Duration redisCacheExpire = Duration.ofMinutes(60L);

}