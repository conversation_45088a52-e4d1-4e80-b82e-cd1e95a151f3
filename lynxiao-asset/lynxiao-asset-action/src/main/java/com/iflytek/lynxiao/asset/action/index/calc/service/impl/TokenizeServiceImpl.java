package com.iflytek.lynxiao.asset.action.index.calc.service.impl;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.base.Stopwatch;
import com.iflytek.lynxiao.asset.action.index.calc.config.IndexCalcProperties;
import com.iflytek.lynxiao.asset.action.index.calc.service.TokenizeService;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.common.feign.LynxiaoFeignClientManager;
import com.iflytek.lynxiao.common.feign.feature.TokenizeApi;
import com.iflytek.lynxiao.common.utils.JsonUtil;
import com.iflytek.lynxiao.data.constant.TraceRecordType;
import com.iflytek.lynxiao.data.domain.AssetCell;
import com.iflytek.lynxiao.data.dto.featurebase.FeatureBaseConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import skynet.boot.pandora.api.ApiRequestPayloadGenerics;
import skynet.boot.pandora.api.ApiResponseGenerics;
import skynet.boot.pandora.brave.TraceUtils;
import skynet.boot.pandora.brave.core.TraceBaseContext;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: leitong
 * @date: 2024/11/7 15:33
 * @description:
 **/
@Slf4j
public class TokenizeServiceImpl implements TokenizeService {

    private final LynxiaoFeignClientManager feignClientManager;
    private final IndexCalcProperties properties;
    private final TraceUtils traceUtils;

    public TokenizeServiceImpl(LynxiaoFeignClientManager feignClientManager, TraceUtils traceUtils, IndexCalcProperties properties) {
        this.feignClientManager = feignClientManager;
        this.properties = properties;
        this.traceUtils = traceUtils;
    }

    @Override
    public List<Map<String, Object>> calc(String traceId, List<AssetCell> docs, FeatureBaseConfigDTO featureBaseConfig) {
        log.debug("[{}} begin to calc tokenize, batch size = {}", traceId, docs.size());
        if (docs.isEmpty()) {
            return Collections.emptyList();
        }
        Assert.notNull(featureBaseConfig.getTokenizationConfig(), "TokenizationConfig should not be null");
        Assert.notEmpty(featureBaseConfig.getTokenizationConfig().getFields(), "TokenizationConfig.fields should not be empty");

        Stopwatch sw = Stopwatch.createStarted();
        // 将计算任务按照服务版本进行分组
        MultiValueMap<String, FeatureBaseConfigDTO.TokenizationField> service2fields = new LinkedMultiValueMap<>();
        for (FeatureBaseConfigDTO.TokenizationField field : featureBaseConfig.getTokenizationConfig().getFields()) {
            service2fields.add(field.getService(), field);
        }
        // 用TreeSet存储中间的分词计算结果
        TreeMap<Integer, Map<String, Object>> offset2Result = new TreeMap<>();
        for (Map.Entry<String, List<FeatureBaseConfigDTO.TokenizationField>> entry : service2fields.entrySet()) {
            // 每个服务进行一次调用
            List<Map<String, Object>> tokenizeResults = this.tokenizeCalc(traceId, docs, entry.getValue(), entry.getKey());
            // 将本轮计算的结果合并到之前的计算结果中
            for (int i = 0; i < docs.size(); i++) {
                Map<String, Object> result = offset2Result.computeIfAbsent(i, offset -> new TreeMap<>());
                result.putAll(tokenizeResults.get(i));
                Long id = docs.get(i).getId();
                Assert.notNull(id, "_id does not exist in docs");
                result.putIfAbsent("_id", id);
            }
        }
        // 最终结果添加到返回值中
        List<Map<String, Object>> ret = new ArrayList<>();
        offset2Result.forEach((i, m) -> ret.add(m));
        log.debug("[{}] tokenize calc success, cost {} ms", traceId, sw.stop().elapsed(TimeUnit.MILLISECONDS));
        return ret;
    }

    private List<Map<String, Object>> tokenizeCalc(String traceId, List<AssetCell> docs, List<FeatureBaseConfigDTO.TokenizationField> fields, String service) {
        List<Object> inputs = tokenizedInput(docs, fields);
        TokenizeApi.TokenizeRequestPayload payload = new TokenizeApi.TokenizeRequestPayload(inputs);
        ApiRequestPayloadGenerics<TokenizeApi.TokenizeRequestPayload> request = new ApiRequestPayloadGenerics<>();
        request.setTraceId(traceId);
        request.setPayload(payload);

        if (Objects.nonNull(this.traceUtils)) {
            TraceBaseContext.init(traceId);
            this.traceUtils.debug(TraceRecordType.TOKENIZE_REQUEST, request.toString(true));
        }

        String serviceUrl = StringUtils.isBlank(this.properties.getTokenizeServiceUrl()) ?
                toTlbUrl(service) : this.properties.getTokenizeServiceUrl();
        log.debug("[traceId={}] tokenize service url is {}", traceId, serviceUrl);
        Assert.notBlank(serviceUrl);
        TokenizeApi tokenizeApi = this.feignClientManager.buildWithUrlFromCache(TokenizeApi.class, serviceUrl);
        ApiResponseGenerics<TokenizeApi.TokenizeResponsePayload> response = tokenizeApi.objs(request);

        if (Objects.nonNull(this.traceUtils)) {
            this.traceUtils.debug(TraceRecordType.TOKENIZE_RESPONSE, response.toString(true));
        }

        if (!TokenizeApi.ok(response)) {
            log.error("[traceId={}] tokenize api request fail : {}", traceId, response.toJson());
            throw new LynxiaoException("tokenize api request fail");
        }
        List<Map<String, Object>> result = response.getPayload().getData().stream().map(JSONObject::from).collect(Collectors.toList());
        result.forEach(doc -> {
            // 分词计算返回的id是string类型，会造成long精度丢失，直接去除id字段
            doc.remove("id");
        });
        return result;
    }

    private static List<Object> tokenizedInput(List<AssetCell> docs, List<FeatureBaseConfigDTO.TokenizationField> fields) {
        List<Object> ret = new ArrayList<>(docs.size());
        for (AssetCell doc : docs) {
            Map<String, Object> inputObj = new TreeMap<>();
            inputObj.put("id", doc.getId());
            inputObj.putAll(tokenizedInput(doc, fields));
            ret.add(inputObj);
        }
        return ret;
    }

    /**
     * 根据索引计算的配置构建单个文档的分词计算输入
     *
     * @param doc    文档对象
     * @param fields 字段配置
     * @return 计算输入
     */
    private static Map<String, Object> tokenizedInput(AssetCell doc, List<FeatureBaseConfigDTO.TokenizationField> fields) {
        Map<String, Object> ret = new TreeMap<>();
        Map<String, FeatureBaseConfigDTO.TokenizationField> calcField2FeatureField = fields.stream()
                .collect(Collectors.toMap(f -> pathSimplify(f.getCalcField()), v -> v));
        Map<String, Object> flattenedDoc = JsonUtil.flatten(doc);
        for (Map.Entry<String, Object> entry : flattenedDoc.entrySet()) {
            String flattenedKey = entry.getKey();
            String simplifiedKey = pathSimplify(flattenedKey);
            if (calcField2FeatureField.containsKey(simplifiedKey)) {
                FeatureBaseConfigDTO.TokenizationField fieldObject = calcField2FeatureField.get(simplifiedKey);
                String featureField = fieldObject.getFeatureField();
                String flattenedFeatureField = FlattenHelper.flattenedFeatureField(flattenedKey, simplifiedKey, featureField);
                String input = trim(fieldObject.getMaxLen(), Optional.ofNullable(entry.getValue()).map(Object::toString).orElse(""));
                ret.put(flattenedFeatureField, input);
            }
        }
        ret.put("id", doc.get("_id"));
        return ret;
    }

    private static String pathSimplify(String fieldPath) {
        return fieldPath.replaceAll("\\[.+\\]", "");
    }

    private static String trim(Integer maxLen, String s) {
        if (StringUtils.isBlank(s)) {
            return "";
        }
        if (Objects.nonNull(maxLen) && maxLen < s.length()) {
            return s.substring(0, maxLen - 1);
        }
        return s;
    }

    private static String toTlbUrl(String serviceName) {
        return String.format("http://tlb:%s", serviceName);
    }
}