package com.iflytek.lynxiao.asset.config;

import com.iflytek.lynxiao.asset.cache.BucketCacheService;
import com.iflytek.lynxiao.asset.cache.MongoClientCacheService;
import com.iflytek.lynxiao.common.cache.AssetPortalCacheService;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import skynet.boot.annotation.EnableSkynetSwagger2;
import skynet.boot.pandora.annotation.EnableSkynetPandora;
import skynet.boot.pandora.ogma.annotation.EnableSkynetPandoraOgma;

@EnableCaching
@EnableSkynetSwagger2
@EnableSkynetPandora
@EnableSkynetPandoraOgma
@Configuration(proxyBeanMethods = false)
@ImportAutoConfiguration(exclude = DataSourceAutoConfiguration.class)
public class ActionConfiguration {

    @Bean
    @ConfigurationProperties("lynxiao.asset.action")
    public ActionProperties actionProperties() {
        return new ActionProperties();
    }

    @Bean
    public MongoClientCacheService mongoClientCacheService() {
        return new MongoClientCacheService();
    }

    @Bean
    public BucketCacheService bucketCacheService(MongoClientCacheService mongoClientCacheService, ActionProperties actionProperties,
                                                 AssetPortalCacheService assetPortalCacheService) {
        return new BucketCacheService(mongoClientCacheService, actionProperties, assetPortalCacheService);
    }
}
