package com.iflytek.lynxiao.asset.action.doc.audit.service;

import cn.hutool.core.thread.BlockPolicy;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.asset.action.doc.audit.config.DocAuditProperties;
import com.iflytek.lynxiao.asset.api.EsServiceApi;
import com.iflytek.lynxiao.asset.api.FeatureBaseApi;
import com.iflytek.lynxiao.asset.cache.BucketCacheService;
import com.iflytek.lynxiao.asset.sender.MqMessageCtrlSender;
import com.iflytek.lynxiao.common.component.FixedCapacityCache;
import com.iflytek.lynxiao.data.domain.AssetCell;
import com.iflytek.lynxiao.data.domain.AssetCellProps;
import com.iflytek.lynxiao.data.dto.action.audit.AssetDocAuditMessage;
import com.iflytek.lynxiao.data.dto.featurebase.FeatureBaseDetailGetDTO;
import com.mongodb.BasicDBObject;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.model.Projections;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.bson.Document;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.util.CollectionUtils;
import skynet.boot.pandora.support.TaskCancelCache;

import java.util.HashMap;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.BiConsumer;

/**
 * 数据审核
 *
 * @author: cwruan
 * @Date: 2025-08-05 14:24
 */

@Slf4j
public class DocAuditServiceImpl implements DocAuditService {
    private static final String STEP_ONE = "1";

    private final BucketCacheService bucketCacheService;
    private final FeatureBaseApi featureBaseApi;
    private final EsServiceApi esServiceApi;
    private final MongoTemplate featureMongoTemplate;
    private final DocAuditProperties properties;
    private final MqMessageCtrlSender ctrlSender;
    private final TaskCancelCache taskCancelCache;
    private final ExecutorService updateExecutor;

    public DocAuditServiceImpl(BucketCacheService bucketCacheService, FeatureBaseApi featureBaseApi,
                               EsServiceApi esServiceApi, MongoTemplate featureMongoTemplate,
                               DocAuditProperties properties, MqMessageCtrlSender ctrlSender,
                               TaskCancelCache taskCancelCache) {
        this.updateExecutor = new ThreadPoolExecutor(properties.getUpdateThreadPoolConfig()
                .getCorePoolSize(), properties.getUpdateThreadPoolConfig()
                .getMaxPoolSize(), 0L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(properties.getUpdateThreadPoolConfig()
                .getQueueCapacity()), new BasicThreadFactory.Builder().namingPattern("audit-update-batch-process-%d")
                .build(), new BlockPolicy());
        this.bucketCacheService = bucketCacheService;
        this.featureMongoTemplate = featureMongoTemplate;
        this.taskCancelCache = taskCancelCache;
        this.featureBaseApi = featureBaseApi;
        this.esServiceApi = esServiceApi;
        this.properties = properties;
        this.ctrlSender = ctrlSender;
    }

    @Override
    public void audit(AssetDocAuditMessage message, String taskId) {
        try {
            log.info("audit doc begin : {}", message);
            String collectionName;
            MongoTemplate template;
            FixedCapacityCache<Long> idsCache;
            // 更新字段
            Map<String, Object> updateMapping = new JSONObject()
                    .fluentPut(AssetCellProps.KEY_A, message.getAuditVal())
                    .fluentPut(AssetCellProps.KEY_AU, message.getAuditor());
            // 判断是否为索引库类型
            if (message.getIsIdxBucket()) {
                FeatureBaseDetailGetDTO featureBase = featureBaseApi.getFeatureBase(IdUtil.fastUUID(), message.getBucketCode());
                collectionName = CollectionUtils.lastElement(featureBase.getMongoFullCollections());
                template = featureMongoTemplate;
                idsCache = buildIdsCache(taskId, updateMapping, (ids, mapping) -> {
                    try {
                        this.esServiceApi.bulkUpdateDocByIds(featureBase, featureBase.getEsFullIndexes(), ids, mapping);
                        updateMongoCell(ids, mapping, template, collectionName);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                });
            } else {
                collectionName = message.getBucketCode();
                template = bucketCacheService.getClient(collectionName).getMongoTemplate();
                idsCache = buildIdsCache(taskId, updateMapping, (ids, mapping) -> {
                    updateMongoCell(ids, mapping, template, collectionName);
                });
            }
            // 执行审核
            executeAudit(message, taskId, collectionName, template, idsCache);
        } catch (Exception e) {
            // todo 目前非流程任务不支持开始就失败逻辑，后续支持后补充
            log.error("audit doc has ex.taskId:{}, e", taskId, e);
        }
    }

    private void executeAudit(AssetDocAuditMessage message, String taskId, String collectionName,
                              MongoTemplate template, FixedCapacityCache<Long> idCache) {
        // 异步第一次汇报进度
        reportFirstTotal(message.getQuery(), collectionName, taskId, template);

        // 扫描数据
        log.info("begin update audit status. taskId:{}, collectionName:{}", taskId, collectionName);
        Document query = Document.parse(message.getQuery());
        AtomicLong realTotal = new AtomicLong();
        try (MongoCursor<Document> cursor = template.getCollection(collectionName).find(query, Document.class)
                .projection(Projections.include(Collections.singletonList(AssetCell.ID))).batchSize(properties.getAuditBatch())
                .noCursorTimeout(true).cursor()) {
            while (cursor.hasNext()) {
                if (this.taskCancelCache.isCancel(taskId)) {
                    log.info("audit had cancel. audit finish. taskId={}", taskId);
                    return;
                }
                realTotal.addAndGet(1);
                Long id = cursor.next().getLong(AssetCell.ID);
                idCache.add(id);
            }
            idCache.flush();
            // 校准发送total
            ctrlSender.sendTotal(STEP_ONE, taskId, realTotal.get());
            log.info("audit has been success. taskId={}. realTotal:{}", taskId, realTotal.get());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private FixedCapacityCache<Long> buildIdsCache(String taskId, Map<String, Object> updateMapping,
                                                   BiConsumer<List<Long>, Map<String, Object>> updateFun) {
        return new FixedCapacityCache<>(properties.getAuditBatch(), ids -> {
            List<Long> copyIds = ids.copy();
            // 使用线程池执行
            updateExecutor.submit(() -> {
                try {
                    Map<String, Object> batchUpdateMapping = new HashMap<>(updateMapping);
                    setTimestamp(batchUpdateMapping);
                    long done = copyIds.size();
                    updateFun.accept(copyIds, batchUpdateMapping);
                    ctrlSender.sendDone(STEP_ONE, taskId, done);
                } catch (Exception e) {
                    log.error("audit data bulk update error. taskId:{}", taskId, e);
                    try {
                        ctrlSender.sendFail(STEP_ONE, taskId, copyIds.size());
                        log.error("fail to update", e);
                    } catch (Exception ex) {
                        log.error("send fail size failed. taskId:{}", taskId, ex);
                    }
                }
            });
        });
    }

    private static void updateMongoCell(List<Long> ids, Map<String, Object> updateMapping, MongoTemplate template,
                                        String collectionName) {
        BasicDBObject filter = new BasicDBObject();
        filter.put(AssetCell.ID, new BasicDBObject("$in", ids));
        BasicDBObject update = new BasicDBObject().append("$set", updateMapping);
        template.getCollection(collectionName).updateMany(filter, update).getModifiedCount();
    }


    /**
     * 异步 首次汇报total
     *
     * @param query          条件
     * @param collectionName 表名
     * @param taskId         任务id
     * @param template       mongo template
     */
    private void reportFirstTotal(String query, String collectionName, String taskId, MongoTemplate template) {
        try {
            long count = getIdsCount(query);
            if (count == 0) {
                count = template.estimatedCount(collectionName);
            }
            ctrlSender.sendTotal(STEP_ONE, taskId, count);
            log.info("first report total success. taskId:{}. total:{}", taskId, count);
        } catch (Exception e) {
            log.error("report total failed. taskId:{}. e:", taskId, e);
        }
    }

    private void setTimestamp(Map<String, Object> updateMapping) {
        long now = System.currentTimeMillis();
        updateMapping.put(AssetCellProps.KEY_UPDATE_TS, now);
        updateMapping.put(AssetCellProps.KEY_AT, now);
    }

    private long getIdsCount(String query) {
        JSONObject jsonObject = JSONObject.parseObject(query);
        JSONObject idCondition = jsonObject.getJSONObject(AssetCell.ID);

        int idCount = 0;
        if (idCondition != null) {
            JSONArray inArray = idCondition.getJSONArray("$in");
            if (inArray != null) {
                idCount = inArray.size();
            }
        }
        return idCount;
    }
}