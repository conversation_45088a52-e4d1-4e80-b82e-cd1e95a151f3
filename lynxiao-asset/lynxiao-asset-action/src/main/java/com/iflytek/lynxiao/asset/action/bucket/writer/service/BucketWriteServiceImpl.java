package com.iflytek.lynxiao.asset.action.bucket.writer.service;

import cn.hutool.core.thread.BlockPolicy;
import com.iflytek.lynxiao.asset.action.bucket.writer.config.BucketWriteProperties;
import com.iflytek.lynxiao.asset.cache.BucketCacheService;
import com.iflytek.lynxiao.asset.cache.CacheMongoTemplate;
import com.iflytek.lynxiao.common.datashard.DatashardEncoder;
import com.iflytek.lynxiao.data.constant.AssetTaskType;
import com.iflytek.lynxiao.data.domain.AssetAuditStatus;
import com.iflytek.lynxiao.data.domain.AssetAuditor;
import com.iflytek.lynxiao.data.domain.AssetCell;
import com.iflytek.lynxiao.data.domain.AssetCellProps;
import com.iflytek.lynxiao.data.dto.action.write.BucketWriteDTO;
import com.iflytek.lynxiao.data.dto.asset.AssetBucketStorageDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.mongodb.core.query.UpdateDefinition;
import org.springframework.data.util.Pair;
import org.springframework.util.CollectionUtils;
import skynet.boot.common.utils.IdUtil;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

/**
 * 数据写入
 *
 * @author: cwruan
 * @Date: 2025-06-04 10:56
 */
@Slf4j
public class BucketWriteServiceImpl implements BucketWriteService {

    private final BucketCacheService bucketCacheService;
    private final BucketWriteProperties bucketWriteProperties;
    private final ExecutorService insertExecutor;
    private final DatashardEncoder datashardEncoder;

    public BucketWriteServiceImpl(BucketCacheService bucketCacheService, BucketWriteProperties bucketWriteProperties, DatashardEncoder datashardEncoder) {
        this.bucketCacheService = bucketCacheService;
        this.bucketWriteProperties = bucketWriteProperties;
        this.datashardEncoder = datashardEncoder;
        this.insertExecutor = new ThreadPoolExecutor(bucketWriteProperties.getThreadPoolConfig().getCorePoolSize(), bucketWriteProperties.getThreadPoolConfig()
                .getMaxPoolSize(), 0L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(bucketWriteProperties.getThreadPoolConfig()
                .getQueueCapacity()), new BasicThreadFactory.Builder().namingPattern("insert-process-%d").build(), new BlockPolicy());
    }

    @Override
    public void write(BucketWriteDTO dto, String taskId, boolean isDebugData) throws Exception {
        // 获取对应桶连接
        CacheMongoTemplate template = bucketCacheService.getClient(dto.getBucketCode());
        AssetBucketStorageDTO bucket = bucketCacheService.getBucket(dto.getBucketCode());
        // 切分
        List<List<AssetCell>> result = split(dto.getData(), bucketWriteProperties.getBatchSize());
        List<Future<?>> futures = new ArrayList<>();
        for (List<AssetCell> batch : result) {
            if (dto.getType() == AssetTaskType.STATE_SYNC.getType()) {
                Future<?> future = processSyncStatus(dto, bucket, template, batch);
                if (future != null) {
                    futures.add(future);
                }
            } else {
                futures.add(processBatch(dto, bucket, template, batch, isDebugData));
            }
        }
        for (Future<?> future : futures) {
            // 获取抛出的异常
            future.get();
        }
    }

    private Future<?> processSyncStatus(BucketWriteDTO dto, AssetBucketStorageDTO bucket, CacheMongoTemplate template, List<AssetCell> batch) {
        // 只处理审核不通过的文档
        List<AssetCell> cells = batch.stream()
                .filter(assetCell -> assetCell.getProps().getAuditStatus() == AssetAuditStatus.REJECT)
                .toList();
        if (CollectionUtils.isEmpty(cells)) {
            return null;
        }
        return insertExecutor.submit(() -> template.bulkUpdate(dto.getBucketCode(), buildSyncStatusUpdates(cells)));
    }

    private Future<?> processBatch(BucketWriteDTO dto, AssetBucketStorageDTO bucket, CacheMongoTemplate template, List<AssetCell> batch, boolean isDebugData) {
        List<AssetCell> docList = batch.stream().peek(assetCell -> {

            assetCell.validateFieldSpec(bucket.getFieldSpec());
            //  系统属性
            AssetCellProps props = assetCell.getProps();
            props.setUts(System.currentTimeMillis());
            props.setCat(Instant.now());
            // 调试状态
            if (isDebugData) {
                props.markAsDebug();
                assetCell.setRefId(assetCell.getId());
                assetCell.setId(IdUtil.getSnowflakeNextId());
            }
            props.setAuditStatus(AssetAuditStatus.of(bucket.getAuditType()), new AssetAuditor());
            assetCell.setProps(props);
        }).toList();
        return insertExecutor.submit(() -> template.bulkUpsert(dto.getBucketCode(), buildUpdates(encode(dto.getBucketCode(), docList))));
    }


    private List<AssetCell> encode(String bucketCode, List<AssetCell> docList) throws Exception {
        List<DatashardEncoder.EncodeDocumentRequest> requestList = docList.stream().map(assetCell -> {
            DatashardEncoder.EncodeDocumentRequest encodeDocumentRequest = new DatashardEncoder.EncodeDocumentRequest();
            encodeDocumentRequest.setDocument(assetCell);
            encodeDocumentRequest.setRefIdPrefix(String.format("%s:%s", bucketCode, assetCell.getId()));
            return encodeDocumentRequest;
        }).toList();
        try {
            List<Map<String, Object>> encodeList = this.datashardEncoder.encode(requestList);
            if (!CollectionUtils.isEmpty(encodeList)) {
                return encodeList.stream().map(AssetCell::from).toList();
            }
        } catch (Exception e) {
            log.error("bucket write encode error", e);
            throw e;
        }
        // 如果调用失败，直接写入原始文档
        return docList;
    }

    private List<Pair<Query, Update>> buildUpdates(List<AssetCell> batch) {
        List<Pair<Query, Update>> updates = new ArrayList<>();
        for (AssetCell cell : batch) {
            Query query = new Query(Criteria.where(AssetCell.ID).is(cell.getId()));
            Update update = new Update();
            cell.keySet().forEach(key -> update.set(key, cell.get(key)));
            updates.add(Pair.of(query, update));
        }
        return updates;
    }

    private List<Pair<Query, UpdateDefinition>> buildSyncStatusUpdates(List<AssetCell> batch) {
        List<Pair<Query, UpdateDefinition>> updates = new ArrayList<>();
        for (AssetCell cell : batch) {
            Query query = new Query(Criteria.where(AssetCell.ID).is(cell.getId()));
            Update update = new Update();
            // 只更新审核字段
            update.set(AssetCellProps.getFilterKey(AssetCellProps.A), cell.getProps().getAuditStatus().getValue())
                    .set(AssetCellProps.getFilterKey(AssetCellProps.AU), cell.getProps().getAu())
                    .set(AssetCellProps.getFilterKey(AssetCellProps.AT), cell.getProps().getAt())
                    .set(AssetCellProps.getFilterKey(AssetCellProps.UTS), System.currentTimeMillis());
            updates.add(Pair.of(query, update));
        }
        return updates;
    }

    private List<List<AssetCell>> split(List<AssetCell> data, int batchSize) {
        List<List<AssetCell>> result = new ArrayList<>();
        int total = data.size();
        for (int i = 0; i < total; i += batchSize) {
            int end = Math.min(i + batchSize, total);
            result.add(data.subList(i, end));
        }
        return result;
    }

}