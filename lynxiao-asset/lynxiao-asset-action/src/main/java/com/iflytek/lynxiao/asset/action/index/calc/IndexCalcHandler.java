package com.iflytek.lynxiao.asset.action.index.calc;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.asset.action.index.calc.service.IndexingService;
import com.iflytek.lynxiao.asset.util.ApiResponseUtils;
import com.iflytek.lynxiao.asset.util.HeaderParser;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.data.constant.IndexingErrorType;
import com.iflytek.lynxiao.data.dto.action.calc.IndexingDTO;
import com.iflytek.lynxiao.data.dto.featurebase.FeatureBaseConfigDTO;
import lombok.extern.slf4j.Slf4j;
import skynet.boot.pandora.MqServiceHandler;
import skynet.boot.pandora.annotation.SkynetPandoraMqHandler;
import skynet.boot.pandora.api.ApiRequest;
import skynet.boot.pandora.api.ApiResponse;
import skynet.boot.pandora.api.PandoraConsts;
import skynet.boot.pandora.exception.PandoraException;
import skynet.boot.pandora.support.MqSessionContext;

/**
 * 索引计算
 *
 * @author: cwruan
 * @Date: 2025-06-04 19:49
 */
@Slf4j
@SkynetPandoraMqHandler
public class IndexCalcHandler implements MqServiceHandler {

    private final IndexingService indexingService;

    public IndexCalcHandler(IndexingService indexingService) {
        this.indexingService = indexingService;
    }

    @Override
    public ApiResponse process(ApiRequest apiRequest) throws PandoraException {
        return null;
    }

    @Override
    public ApiResponse process(MqSessionContext sessionContext, ApiRequest apiRequest) throws PandoraException {
        log.debug("process IndexingHandler apiRequest={}", apiRequest);
        // 解析请求头中的任务id
        String taskId = HeaderParser.get(PandoraConsts.PANDORA_TASK_ID);
        try {
            String traceId = apiRequest.getTraceId();
            IndexingDTO indexingDTO = convertPayload(apiRequest.getPayload());
            this.indexingService.process(indexingDTO, sessionContext, traceId, taskId);

        } catch (Exception e) {
            log.error("indexing process has ex:", e);
            try {
                if (e instanceof LynxiaoException lynxiaoException) {
                    sessionContext.send(ApiResponseUtils.getApiResponse(lynxiaoException.getCode(), e.getMessage()));
                } else {
                    sessionContext.send(ApiResponseUtils.getApiResponse(IndexingErrorType.DEFAULT_ERROR.getCode(), e.getMessage()));
                }
            } catch (Exception ex) {
                log.error("sessionContext send has ex:", ex);
            }
        }
        return null;
    }


    private IndexingDTO convertPayload(JSONObject payload) {
        // 兼容历史向量字段数据,若出现异常，则向量配置出现问题，不再继续处理
        JSONObject vectorField = payload.getJSONObject("featureBaseConfig").getJSONObject("vectorConfig").getJSONArray("fields").getJSONObject(0);
        String concatString = vectorField.getString("concatString");
        JSONArray calcFields = vectorField.getJSONArray("calcFields");
        JSONArray calcFieldList = new JSONArray();
        for (Object calcField : calcFields) {
            if (calcField instanceof String) {
                calcFieldList.add(new FeatureBaseConfigDTO.CalcField((String) calcField, concatString));
            } else {
                calcFieldList.add(calcField);
            }
        }
        vectorField.put("calcFields", calcFieldList);
        payload.getJSONObject("featureBaseConfig").getJSONObject("vectorConfig").getJSONArray("fields").set(0, vectorField);
        return payload.to(IndexingDTO.class);
    }
}