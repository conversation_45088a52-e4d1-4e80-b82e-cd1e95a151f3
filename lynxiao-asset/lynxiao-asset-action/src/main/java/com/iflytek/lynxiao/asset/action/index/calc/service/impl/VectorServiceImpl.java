package com.iflytek.lynxiao.asset.action.index.calc.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.UnicodeUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.base.Preconditions;
import com.google.common.base.Stopwatch;
import com.iflytek.lynxiao.asset.action.index.calc.service.VectorService;
import com.iflytek.lynxiao.asset.action.index.calc.vector.service.VectorProxyService;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.common.feign.feature.VectorApi;
import com.iflytek.lynxiao.common.utils.JsonPathExpandAndConcat;
import com.iflytek.lynxiao.data.constant.Fields;
import com.iflytek.lynxiao.data.constant.IndexingErrorType;
import com.iflytek.lynxiao.data.constant.TraceRecordType;
import com.iflytek.lynxiao.data.domain.AssetCell;
import com.iflytek.lynxiao.data.dto.featurebase.FeatureBaseConfigDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import skynet.boot.exception.SkynetException;
import skynet.boot.pandora.api.ApiRequestPayloadGenerics;
import skynet.boot.pandora.api.ApiResponseGenerics;
import skynet.boot.pandora.brave.TraceUtils;
import skynet.boot.pandora.brave.core.TraceBaseContext;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: leitong
 * @date: 2024/11/7 14:48
 * @description:
 **/
@Slf4j
public class VectorServiceImpl implements VectorService {

    private final VectorProxyService vectorService;
    private final TraceUtils traceUtils;

    public VectorServiceImpl(TraceUtils traceUtils, VectorProxyService vectorService) {
        this.traceUtils = traceUtils;
        this.vectorService = vectorService;
    }

    @Override
    public List<AssetCell> calc(String traceId, String site, List<AssetCell> docs, FeatureBaseConfigDTO featureBaseConfig) {
        if (docs.isEmpty()) {
            return Collections.emptyList();
        }
        Assert.notNull(featureBaseConfig.getVectorConfig(), "VectorConfig should not be null");
        Assert.equals(featureBaseConfig.getVectorConfig().getFields().size(), 1, "vectorConfig.fields only support for length 1");
        FeatureBaseConfigDTO.VectorField vectorField = featureBaseConfig.getVectorConfig().getFields().getFirst();
        log.debug("[{}} begin to calc vector, batch size : {}", traceId, docs.size());

        // 校验正文不存在的文档，如果有则直接抛出异常
        List<String> calcFieldNameList = vectorField.getCalcFields().stream()
                .map(obj -> JSONObject.parseObject(JSONObject.toJSONString(obj), FeatureBaseConfigDTO.CalcField.class).getName())
                .toList();
        if (calcFieldNameList.contains(AssetCell.CONTENT_FIELD)) {
            for (AssetCell doc : docs) {
                if (doc.getContentOptional().isEmpty() || StringUtils.isBlank(doc.getContentOptional().get())) {
                    log.error("[traceId={}] doc[{}] content is not present or blank", traceId, doc.getId());
                    throw new LynxiaoException(IndexingErrorType.CONTENT_NOT_PRESENT.getCode(), IndexingErrorType.CONTENT_NOT_PRESENT.getName());
                }
            }
        }

        // 向量计算
        return vectorCalc(traceId, site, docs, vectorField);
    }


    /**
     * 调用外部接口进行计算
     */
    private List<AssetCell> vectorCalc(String traceId, String site,
                                       List<AssetCell> docs,
                                       FeatureBaseConfigDTO.VectorField fieldConfig) {
        Stopwatch sw = Stopwatch.createStarted();
        List<AssetCell> ret = new ArrayList<>();
        Assert.notNull(fieldConfig, "fieldConfig must not be null");
        List<String> inputTexts = new ArrayList<>();
        List<Long> inputIds = new ArrayList<>();
        Map<Integer, VectorInput> idx2VectorInput = new TreeMap<>();
        for (AssetCell doc : docs) {
            List<VectorInput> vectorInputs = generateVectorInput(doc, fieldConfig);
            for (VectorInput vectorInput : vectorInputs) {
                inputTexts.add(vectorInput.text);
                inputIds.add(vectorInput.id);
                idx2VectorInput.put(inputTexts.size() - 1, vectorInput);
            }
        }
        Map<Long, JSONObject> id2result = new TreeMap<>();
        List<float[]> vectors = this.fetchVector(traceId, site, inputIds, inputTexts, fieldConfig);
        Assert.equals(vectors.size(), inputTexts.size(), "vector result length is not equal to input docs length");
        for (int i = 0; i < vectors.size(); i++) {
            float[] vector = vectors.get(i);
            VectorInput vectorInput = idx2VectorInput.get(i);
            JSONObject result = id2result.computeIfAbsent(vectorInput.id, k -> new JSONObject());
            JSONArray embeddings = (JSONArray) result.computeIfAbsent(AssetCell.EMBEDDINGS, k -> new JSONArray());
            List<String> jsonPaths = vectorInput.kvList.stream().map(KV::getKey).toList();
            embeddings.add(new JSONObject().fluentPut(AssetCell.EMBEDDING, vector).fluentPut(Fields.JSON_PATHS, jsonPaths));
        }
        for (AssetCell doc : docs) {
            Long id = doc.getId();
            Map<String, Object> result = id2result.get(id);
            // 把vector 结果 组装成 AssetCell
            result.put("_id", id);
            ret.add(AssetCell.from(result));
        }
        log.debug("[traceId={}] vector calc success, cost {} ms", traceId, sw.stop().elapsed(TimeUnit.MILLISECONDS));
        return ret;
    }

    private List<float[]> fetchVector(String traceId, String site, List<Long> ids,
                                      List<String> inputs, FeatureBaseConfigDTO.VectorField fieldConfig) {

        ApiRequestPayloadGenerics<VectorApi.VectorRequestPayload> request = this.buildVectorRequest(traceId, site, fieldConfig.getService(), ids, inputs);
        if (Objects.nonNull(this.traceUtils)) {
            TraceBaseContext.init(traceId);
            this.traceUtils.debug(TraceRecordType.VECTOR_REQUEST, request.toString(true));
        }

        ApiResponseGenerics<VectorApi.VectorResponsePayload> response = null;
        try {
            response = vectorService.semantic(request);
        } catch (SkynetException e) {
            log.error("vector api request has error: code={}; msg={}", e.getCode(), e.getMessage());
            throw new LynxiaoException(e.getCode(), String.format("vector api request has ex: %s", e.getMessage()));
        } catch (Exception e) {
            log.error("vector api request has ex:", e);
            throw new LynxiaoException(String.format("vector api request has ex: %s", e.getMessage()));
        }

        if (Objects.nonNull(this.traceUtils)) {
            this.traceUtils.debug(TraceRecordType.VECTOR_RESPONSE, response.toString(true));
        }

        if (!VectorApi.ok(response)) {
            log.error("[traceId={}] vector api request fail : {}", traceId, response.toJson());
            throw new LynxiaoException(String.format("vector api request fail: %s", response.toJson()));
        }

        List<float[]> embeddings = response.getPayload().getEmbedding();
        Preconditions.checkArgument(embeddings.size() == inputs.size(),
                "[%s] embeddings size is not equal to request.payload", traceId);
        return embeddings;
    }

    private ApiRequestPayloadGenerics<VectorApi.VectorRequestPayload> buildVectorRequest(String traceId, String site,
                                                                                         String version, List<Long> ids,
                                                                                         List<String> texts) {
        Assert.notBlank(version, "vectorConfig.service must not be blank");

        ApiRequestPayloadGenerics<VectorApi.VectorRequestPayload> request = new ApiRequestPayloadGenerics<>();
        VectorApi.VectorRequestPayload payload = new VectorApi.VectorRequestPayload(texts);
        request.setTraceId(traceId);
        request.setPayload(payload);

        VectorApi.VectorRequestContext context = new VectorApi.VectorRequestContext();
        context.setSite(site);
        context.setVersion(version);
        context.setDocIds(ids);
        payload.setContext(context);

        return request;
    }

    private static List<VectorInput> generateVectorInput(AssetCell doc, FeatureBaseConfigDTO.VectorField vectorField) {
        List<VectorInput> ret = new ArrayList<>();

        Map<String, String> nameAndConnectStrMap = vectorField.getCalcFields().stream()
                .map(obj -> JSONObject.parseObject(JSONObject.toJSONString(obj), FeatureBaseConfigDTO.CalcField.class))
                .collect(Collectors.toMap(
                        calcField -> jsonPath(calcField.getName()),
                        FeatureBaseConfigDTO.CalcField::getConcatString,
                        (existing, replacement) -> existing, // 处理重复key的策略
                        LinkedHashMap::new // 指定使用LinkedHashMap
                ));


        List<List<KV>> kvListList = flattenedJsonPath2Value(doc, new ArrayList<>(nameAndConnectStrMap.keySet()));
        for (List<KV> kvList : kvListList) {
            VectorInput vectorInput = new VectorInput();
            vectorInput.id = doc.getId();
            vectorInput.kvList = join(nameAndConnectStrMap, kvList);
            vectorInput.text = buildText(vectorInput.id, vectorInput.kvList);
            ret.add(vectorInput);
        }
        return ret;
    }

    private static List<KV> join(Map<String, String> nameAndConnectStrMap, List<KV> list) {
        List<KV> ret = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            ret.add(list.get(i));
            if (i != (list.size() - 1)) {
                ret.add(getConnectStr(nameAndConnectStrMap, list.get(i + 1).key));
            }
        }
        return ret;
    }

    private static KV getConnectStr(Map<String, String> nameAndConnectStrMap, String realJsonPath) {
        for (String calcFieldPath : nameAndConnectStrMap.keySet()) {
            if (JsonPathExpandAndConcat.isPathMatch(realJsonPath, calcFieldPath)) {
                return new KV(nameAndConnectStrMap.get(calcFieldPath), nameAndConnectStrMap.get(calcFieldPath));
            }
        }
        return new KV(StringUtils.EMPTY, StringUtils.EMPTY);
    }

    private static List<List<KV>> flattenedJsonPath2Value(Map<String, Object> doc, List<String> jsonPaths) {
        JSONObject jsonObject = JSONObject.from(doc);
        String[] jsonPathArray = jsonPaths.toArray(new String[0]);
        List<List<Map<String, Object>>> result = JsonPathExpandAndConcat.expandJsonPathsSmart(jsonObject, jsonPathArray);

        return result.stream()
                .map(lst -> lst.stream().map(KV::from).toList())
                .toList();
    }

    private static String buildText(Long id, List<KV> kvList) {
        StringBuilder sb = new StringBuilder();
        for (KV kv : kvList) {
            String value = kv.value;
            if (StringUtils.isBlank(value)) {
                log.warn("doc[{}] : value of key {} is blank", id, kv.key);
            }
            if (value.startsWith("\\u")) {
                value = UnicodeUtil.toString(value);
            }
            sb.append(value);
        }
        return sb.toString();
    }

    private static String jsonPath(String field) {
        return "$." + field;
    }

    private static class VectorInput {

        private Long id;

        private List<KV> kvList;

        private String text = "";

    }

    @Getter
    @Setter
    private static class KV {
        private String key;
        private String value;

        public KV() {
        }

        public KV(String key, String value) {
            this.key = key;
            this.value = value;
        }

        public static KV from(Map<String, Object> map) {
            KV ret = new KV();
            Object key = map.get("key");
            Assert.isTrue(Objects.nonNull(key), "invalid jsonpath value map : %s", JSON.toJSONString(map));
            ret.key = map.get("key").toString();
            ret.value = Optional.ofNullable(map.get("value")).map(Object::toString).orElse("");
            return ret;
        }
    }

}