package com.iflytek.lynxiao.asset.action.bucket.reader.config;

import com.iflytek.lynxiao.asset.action.bucket.reader.condition.ConditionalOnBucketReader;
import com.iflytek.lynxiao.asset.action.bucket.reader.service.BucketReadService;
import com.iflytek.lynxiao.asset.action.bucket.reader.service.BucketReadServiceImpl;
import com.iflytek.lynxiao.asset.cache.BucketCacheService;
import com.iflytek.lynxiao.resource.repository.AssetTaskBatchRepository;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import skynet.boot.pandora.brave.TraceUtils;
import skynet.boot.pandora.ogma.PandoraApiRequestObserverBuilder;
import skynet.boot.pandora.support.TaskCancelCache;

/**
 * 自动配置
 *
 * @author: cwruan
 * @Date: 2025-05-29 20:30
 */
@ConditionalOnBucketReader
@Configuration(proxyBeanMethods = false)
@Import({
        org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration.class,
        org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration.class
})
public class BucketReadConfiguration {

    @Bean
    @ConfigurationProperties("lynxiao.asset.action.bucket.read")
    public BucketReadProperties bucketReadProperties() {
        return new BucketReadProperties();
    }

    @Bean
    public BucketReadService bucketReadService(PandoraApiRequestObserverBuilder apiRequestObserverBuilder, BucketCacheService bucketCacheService,
                                               BucketReadProperties bucketReadProperties, AssetTaskBatchRepository assetTaskBatchRepository,
                                               TaskCancelCache taskCancelCache, TraceUtils traceUtils, MeterRegistry meterRegistry) {
        return new BucketReadServiceImpl(apiRequestObserverBuilder, bucketCacheService, bucketReadProperties,
                assetTaskBatchRepository, taskCancelCache, traceUtils, meterRegistry);
    }
}