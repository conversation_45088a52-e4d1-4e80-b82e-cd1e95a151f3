package com.iflytek.lynxiao.asset.action.bucket.reader;

import cn.hutool.core.thread.BlockPolicy;
import com.iflytek.lynxiao.asset.action.bucket.reader.condition.ConditionalOnBucketReader;
import com.iflytek.lynxiao.asset.action.bucket.reader.config.BucketReadProperties;
import com.iflytek.lynxiao.asset.action.bucket.reader.service.BucketReadService;
import com.iflytek.lynxiao.asset.util.ApiResponseUtils;
import com.iflytek.lynxiao.asset.util.HeaderParser;
import com.iflytek.lynxiao.data.constant.Fields;
import com.iflytek.lynxiao.data.constant.IndexingErrorType;
import com.iflytek.lynxiao.data.dto.action.read.BucketReadDTO;
import com.iflytek.lynxiao.data.message.MessageHeaderConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.slf4j.MDC;
import org.springframework.util.CollectionUtils;
import skynet.boot.pandora.MqServiceHandler;
import skynet.boot.pandora.annotation.SkynetPandoraMqHandler;
import skynet.boot.pandora.api.ApiRequest;
import skynet.boot.pandora.api.ApiResponse;
import skynet.boot.pandora.api.PandoraConsts;
import skynet.boot.pandora.brave.TraceUtils;
import skynet.boot.pandora.exception.PandoraException;
import skynet.boot.pandora.support.MqSessionContext;

import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 读取桶中的数据
 *
 * @author: cwruan
 * @Date: 2025-05-29 13:39
 */
@Slf4j
@ConditionalOnBucketReader
@SkynetPandoraMqHandler
public class BucketReadHandler implements MqServiceHandler {

    private final BucketReadService service;
    private final BucketReadProperties properties;
    private final ThreadPoolExecutor readExecutor;
    private final TraceUtils traceUtils;

    public BucketReadHandler(BucketReadService service, BucketReadProperties properties, TraceUtils traceUtils) {
        this.service = service;
        this.properties = properties;
        this.readExecutor = new ThreadPoolExecutor(properties.getThreadPoolConfig().getCorePoolSize(), properties.getThreadPoolConfig()
                .getMaxPoolSize(), 0L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(properties.getThreadPoolConfig()
                .getQueueCapacity()), new BasicThreadFactory.Builder().namingPattern("read-process-%d").build(), new BlockPolicy());
        this.traceUtils = traceUtils;
    }

    @Override
    public ApiResponse process(ApiRequest apiRequest) throws PandoraException {
        return null;
    }

    @Override
    public ApiResponse process(MqSessionContext sessionContext, ApiRequest apiRequest) throws PandoraException {
        // 解析请求头中的任务id
        String taskId = HeaderParser.get(PandoraConsts.PANDORA_TASK_ID);
        boolean isRetry = Boolean.parseBoolean(HeaderParser.get(MessageHeaderConstant.PANDORA_TASK_RETRY));
        boolean isDebug = "true".equals(HeaderParser.get(PandoraConsts.PANDORA_TRACE_DEBUG));
        try {
            traceUtils.info("BucketRead", apiRequest, Map.of("executionId", taskId));
        } catch (Exception e) {
            log.info("AssetTaskStateCallback#trace error: " + e.getMessage());
        }
        MDC.put(Fields.TASK_ID, taskId);
        log.info("【bucket-read-req】 request:{}", apiRequest);

        try {
            BucketReadDTO bucketReadDTO = apiRequest.getPayload().to(BucketReadDTO.class);
            if (bucketReadDTO.getBatch() == 0) {
                bucketReadDTO.setBatch(properties.getDefaultBatch());
            }
            // 校验
            checkParam(bucketReadDTO, isDebug, isRetry, taskId);
            // 防止定时任务执行期间，实时数据正在写入，导致时间戳之间有差值漏掉了部分数据, 比如扫数据结束后，同样的时间戳还在消费kafka数据
            TimeUnit.MILLISECONDS.sleep(properties.getTaskDelay());
            submitTask(sessionContext.copy(), bucketReadDTO, taskId, isRetry, isDebug);
        } catch (Exception e) {
            // 失败返回错误码
            log.error("【bucket-read-error】 taskId:{}, apiRequest:{}, error:{}", taskId, apiRequest, e.getMessage(), e);
            try {
                sessionContext.send(ApiResponseUtils.getApiResponse(IndexingErrorType.READER_ERROR.getCode(), e.getMessage()));
            } catch (Exception ex) {
                log.error("【bucket-read-send-error】 failed to send error response", ex);
            }
        }
        log.info("【bucket-read-finish】 task:{}", taskId);
        return null;
    }

    // 校验
    private void checkParam(BucketReadDTO dto, boolean isDebug, boolean isRetry, String taskId) {
        if (!isRetry && (CollectionUtils.isEmpty(dto.getBucketCodeList()) || StringUtils.isAnyBlank(taskId))) {
            throw new PandoraException("bucketCode or taskId is empty!");
        }
        if (isDebug && (CollectionUtils.isEmpty(dto.getIds()) && (dto.getCellNum() == null || dto.getCellNum() == 0))) {
            throw new PandoraException("ids or cellNum is empty!");
        }
    }

    private void submitTask(MqSessionContext sessionContext, BucketReadDTO bucketReadDTO, String taskId, boolean isRetry, boolean isDebug) {
        CompletableFuture.runAsync(() -> {
            if (isRetry) {
                this.service.retry(sessionContext, taskId);
            } else {
                this.service.scan(sessionContext, bucketReadDTO, taskId, isDebug);
            }
        }, readExecutor);
    }
}
