package com.iflytek.lynxiao.asset.api;


import cn.hutool.core.thread.ThreadUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.ErrorCause;
import co.elastic.clients.elasticsearch.core.BulkRequest;
import co.elastic.clients.elasticsearch.core.BulkResponse;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.bulk.BulkOperation;
import co.elastic.clients.elasticsearch.core.bulk.BulkResponseItem;
import co.elastic.clients.elasticsearch.core.bulk.UpdateOperation;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.transport.ElasticsearchTransport;
import co.elastic.clients.transport.rest_client.RestClientTransport;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.base.Preconditions;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.RemovalListener;
import com.iflytek.lynxiao.asset.action.index.sink.domain.ProcessResult;
import com.iflytek.lynxiao.asset.action.index.sink.exception.EsCircuitedBreakingException;
import com.iflytek.lynxiao.asset.config.ActionProperties;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.common.utils.JsonUtil;
import com.iflytek.lynxiao.data.domain.AssetCell;
import com.iflytek.lynxiao.data.dto.action.sink.IndexDbWriteInputDTO;
import com.iflytek.lynxiao.data.dto.featurebase.EsClusterDTO;
import com.iflytek.lynxiao.data.dto.featurebase.FeatureBaseDetailGetDTO;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Tags;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.ssl.SSLContextBuilder;
import org.elasticsearch.client.ResponseException;
import org.elasticsearch.client.RestClient;
import org.springframework.util.CollectionUtils;
import skynet.boot.pandora.brave.annotation.PandoraMetric;
import skynet.boot.pandora.brave.metric.PandoraMetricHolder;

import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: leitong
 * @date: 2025/4/1 17:09
 * @description: ES交互服务
 **/
@Slf4j
public class EsServiceApi implements AutoCloseable {


    /**
     * key: ES集群记录Id, value: ES客户端
     */
    private final Cache<Long, ElasticsearchClient> cachedClients;

    private final ActionProperties properties;

    private final FeatureBaseApi featureBaseApi;

    private final MeterRegistry meterRegistry;


    public EsServiceApi(ActionProperties properties, FeatureBaseApi featureBaseApi, MeterRegistry meterRegistry) {
        cachedClients = CacheBuilder.newBuilder()
                .expireAfterAccess(1, TimeUnit.DAYS)
                .removalListener(this.newRemovalListener()).build();
        this.properties = properties;
        this.featureBaseApi = featureBaseApi;
        this.meterRegistry = meterRegistry;
    }

    @PandoraMetric("sink.es.write")
    public ProcessResult process(String traceId, IndexDbWriteInputDTO input) throws IOException {
        return executeOperation(traceId, input, "upsert", this::buildUpsertOperationsWrapper);
    }

    /**
     * 处理删除操作
     */
    @PandoraMetric("sink.es.delete")
    public ProcessResult processDelete(String traceId, IndexDbWriteInputDTO input) throws IOException {
        return executeOperation(traceId, input, "delete", this::buildDeleteOperationsWrapper);
    }

    /**
     * 通用的操作执行方法，消除process和processDelete的重复代码
     *
     * @param traceId          跟踪ID
     * @param input            输入数据
     * @param operationType    操作类型（用于日志和指标）
     * @param operationBuilder 具体的操作构建逻辑
     * @return 处理结果
     */
    private ProcessResult executeOperation(String traceId, IndexDbWriteInputDTO input,
                                           String operationType,
                                           OperationBuilder operationBuilder) throws IOException {
        if (CollectionUtils.isEmpty(input.getData())) {
            return new ProcessResult();
        }

        ProcessResult ret = null;
        try {
            FeatureBaseDetailGetDTO featureBase = featureBaseApi.getFeatureBase(traceId, input.getBucketCode());
            ElasticsearchClient client = client(featureBase.getEsClusters().getFull());

            // 删除操作不需要过滤字段，只需要文档ID
            List<Map<String, Object>> processedDocs = "delete".equals(operationType)
                    ? new ArrayList<>(input.getData()) : this.filter(input.getData(), featureBase);

            List<BulkOperation> operations = operationBuilder.buildOperations(
                    client, featureBase, processedDocs, traceId);

            log.debug("[{}] ES {} operations count:{}", traceId, operationType, operations.size());
            if (log.isTraceEnabled()) {
                log.trace("[{}] {} operations : {}", traceId, operationType, JSON.toJSONString(processedDocs.stream()
                        .map(doc -> doc.get("_id")).toList()));
            }
            ret = executeBulkOperations(client, operations, input.getData());
        } finally {
            String successTag = String.valueOf(Objects.nonNull(ret) && ret.isSuccess());
            Tags tags = Tags.of(Tag.of("op", operationType), Tag.of("success", successTag));
            PandoraMetricHolder.setTags(tags);
            meterRegistry.counter(
                    String.format("lynxiao.sink.es.%s.total", operationType), tags
            ).increment(input.getData().size());
        }

        return ret;
    }

    public long bulkUpdateDocByIds(FeatureBaseDetailGetDTO featureBase, List<String> indexes, List<Long> ids, Map<String, Object> updates) throws IOException {
        ElasticsearchClient client = this.client(featureBase.getEsClusters().getFull());
        List<BulkOperation> bulkOperations = new ArrayList<>();
        for (String index : indexes) {
            for (Long id : ids) {
                UpdateOperation<Object, Object> updateOperation = UpdateOperation.of(fn -> fn.id(id.toString()).index(index)
                        .action(action -> action.doc(convert(updates))));
                BulkOperation bulkOperation = BulkOperation.of(fn -> fn.update(updateOperation));
                bulkOperations.add(bulkOperation);

            }
        }
        BulkRequest bulkRequest = BulkRequest.of(fn -> fn.operations(bulkOperations));
        BulkResponse bulkResponse = client.bulk(bulkRequest);
        if (bulkResponse.errors()) {
            List<BulkResponseItem> errorItems = bulkResponse.items().stream()
                    .filter(item -> item.error() != null && item.status() != 404) // 忽略404错误
                    .toList();
            if (!errorItems.isEmpty()) {
                log.error("ES Bulk update failed with errors，indexes: {}, ids: {}, updates: {}， bulkResponse：{}", indexes, ids, updates, bulkResponse);
                throw new LynxiaoException("es批量更新失败！");
            }
        }
        return bulkResponse.items().stream().filter(item -> Objects.isNull(item.error())).count();
    }

    /**
     * 操作构建器函数式接口
     */
    @FunctionalInterface
    private interface OperationBuilder {
        List<BulkOperation> buildOperations(ElasticsearchClient client,
                                            FeatureBaseDetailGetDTO featureBase,
                                            List<Map<String, Object>> processedDocs,
                                            String traceId) throws IOException;
    }

    /**
     * upsert操作构建器包装方法
     */
    private List<BulkOperation> buildUpsertOperationsWrapper(ElasticsearchClient client,
                                                             FeatureBaseDetailGetDTO featureBase,
                                                             List<Map<String, Object>> processedDocs,
                                                             String traceId) throws IOException {
        if (featureBase.getEsFullIndexes().size() < 2) {
            return buildUpsertOperations(processedDocs, featureBase.getCurrenteEsFullIndex());
        } else {
            Map<String, Map<String, Object>> id2doc = processedDocs.stream()
                    .collect(Collectors.toMap(doc -> doc.get("_id").toString(), doc -> doc));
            Map<String, String> id2indice = getActualIndices(client, featureBase.getEsFullIndexes(),
                    new ArrayList<>(id2doc.keySet()));
            return buildUpsertOperations(id2doc, id2indice, featureBase.getCurrenteEsFullIndex());
        }
    }

    /**
     * delete操作构建器包装方法
     */
    private List<BulkOperation> buildDeleteOperationsWrapper(ElasticsearchClient client,
                                                             FeatureBaseDetailGetDTO featureBase,
                                                             List<Map<String, Object>> processedDocs,
                                                             String traceId) throws IOException {
        if (featureBase.getEsFullIndexes().size() < 2) {
            return buildDeleteOperations(processedDocs, featureBase.getCurrenteEsFullIndex());
        } else {
            Map<String, Map<String, Object>> id2doc = processedDocs.stream()
                    .collect(Collectors.toMap(doc -> doc.get("_id").toString(), doc -> doc));
            Map<String, String> id2indice = getActualIndices(client, featureBase.getEsFullIndexes(),
                    new ArrayList<>(id2doc.keySet()));
            return buildDeleteOperations(id2doc, id2indice);
        }
    }

    private ProcessResult executeBulkOperations(ElasticsearchClient client, List<BulkOperation> operations,
                                                List<AssetCell> data) throws IOException {
        ProcessResult ret = null;
        int i = 0;
        while (Objects.isNull(ret) && i < properties.getEsWriteRetryTimes()) {
            try {
                BulkResponse response = client.bulk(b -> b.operations(operations));
                ret = handleBulkResponse(response, data);
            } catch (ResponseException | EsCircuitedBreakingException e) {
                if (i == properties.getEsWriteRetryTimes() - 1) {
                    throw e;
                }
                if (e instanceof ResponseException responseException) {
                    if (responseException.getResponse().getStatusLine()
                            .getStatusCode() != EsCircuitedBreakingException.STATUS) {
                        throw e;
                    }
                }
                log.error("EsCircuiteBreakingException, wait for {} and retry", properties.getEsWriteRetryInterval());
                ThreadUtil.safeSleep(properties.getEsWriteRetryInterval().toMillis());
                meterRegistry.counter("lynxiao.sink.es.write.retries").increment();
            } finally {
                i++;
            }
        }
        return ret;
    }

    public Map<String, String> getActualIndices(ElasticsearchClient client, List<String> indices,
                                                List<String> ids) throws IOException {
        // 用实际索引查询对应的文档
        Map<String, String> idToIndexMap = new HashMap<>();
        for (String index : indices) {
            SearchRequest searchRequest = SearchRequest.of(s -> s
                    .index(index)
                    .query(q -> q
                            .ids(i -> i
                                    .values(ids)
                            )
                    )
            );
            SearchResponse<Object> searchResponse = client.search(searchRequest, Object.class);
            for (Hit<Object> hit : searchResponse.hits().hits()) {
                idToIndexMap.put(hit.id(), index); // 将 ID 和索引名称存入映射
            }
        }

        return idToIndexMap; // 返回 ID 到索引名称的映射
    }

    /**
     * 过滤掉payload数据中不需要入到ES的字段
     */
    private List<Map<String, Object>> filter(List<AssetCell> data, FeatureBaseDetailGetDTO featureBase) {
        Preconditions.checkNotNull(featureBase.getFeatureBaseConfig());
        Set<String> fields = new HashSet<>(featureBase.getFeatureBaseConfig().parseEsIncludeFields());
        return data.stream().map(doc -> this.filter(doc, fields)).collect(Collectors.toList());
    }

    /**
     * 过滤掉doc中不需要入到ES的字段
     */
    private Map<String, Object> filter(Map<String, Object> doc, Set<String> fields) {
        JSONObject jsonObject = JSONObject.from(doc);
        List<String> jsonPaths = fields.stream().map(JsonUtil::toJsonPath).toList();
        return JsonUtil.filterJson(jsonObject, jsonPaths);
    }

    /**
     * 根据文档在多个索引的存在情况构建批量的upsert操作
     */
    private static List<BulkOperation> buildUpsertOperations(Map<String, Map<String, Object>> id2doc,
                                                             Map<String, String> id2indice, String currentIndice) {
        List<BulkOperation> operations = new ArrayList<>();
        for (Map.Entry<String, Map<String, Object>> id2docEntry : id2doc.entrySet()) {
            String id = id2docEntry.getKey();
            String indice = id2indice.get(id);
            Map<String, Object> newDoc = new HashMap<>(id2docEntry.getValue());
            newDoc.put("id", id);
            newDoc.remove("_id");
            if (Objects.isNull(indice)) {
                operations.add(BulkOperation.of(b -> b.index(i -> i.index(currentIndice).id(id).document(newDoc))));
            } else {
                operations.add(BulkOperation.of(b -> b
                        .update(u -> u
                                .index(indice)
                                .id(id)
                                .action(a -> a
                                        .doc(newDoc)
                                        .docAsUpsert(false)
                                )
                        )
                ));
            }
        }
        return operations;
    }

    /**
     * 针对当前的写入索引构建批量的upsert操作
     */
    private static List<BulkOperation> buildUpsertOperations(List<Map<String, Object>> data, String indice) {
        List<BulkOperation> operations = new ArrayList<>();
        for (Map<String, Object> doc : data) {
            String id = doc.get("_id").toString();
            Map<String, Object> newDoc = new HashMap<>(doc);
            newDoc.put("id", id);
            newDoc.remove("_id");
            operations.add(BulkOperation.of(b -> b
                    .update(u -> u
                            .index(indice)
                            .id(id)
                            .action(a -> a
                                    .doc(newDoc)
                                    .docAsUpsert(true)
                            )
                    )
            ));
        }
        return operations;
    }


    public static List<BulkOperation> buildDeleteOperations(List<Map<String, Object>> data, String indexName) {
        List<BulkOperation> operations = new ArrayList<>();
        for (Map<String, Object> doc : data) {
            String id = doc.getOrDefault("_id", "1234567890").toString();
            if (id.equals("1234567890")) {
                log.warn("The id is null; doc={}", JSON.toJSONString(doc));
            }
            operations.add(BulkOperation.of(b -> b
                    .delete(d -> d
                            .index(indexName)
                            .id(id)
                    )
            ));
        }
        return operations;
    }

    /**
     * 构建删除操作（多索引版本）
     */
    public static List<BulkOperation> buildDeleteOperations(Map<String, Map<String, Object>> id2doc,
                                                            Map<String, String> id2indice) {
        List<BulkOperation> operations = new ArrayList<>();
        for (Map.Entry<String, Map<String, Object>> entry : id2doc.entrySet()) {
            String id = entry.getKey();
            String indexName = id2indice.get(id);
            if (indexName == null) {
                log.warn("No index found for document id: {}", id);
                continue;
            }
            operations.add(BulkOperation.of(b -> b
                    .delete(d -> d
                            .index(indexName)
                            .id(id)
                    )
            ));
        }
        return operations;
    }

    private ElasticsearchClient client(EsClusterDTO dto) {
        ElasticsearchClient ret;
        try {
            ret = this.cachedClients.get(dto.getId(), () -> this.buildClient(dto));
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
        return ret;
    }

    /**
     * 处理ES批量操作结果
     */
    private ProcessResult handleBulkResponse(BulkResponse response,
                                             List<AssetCell> originalData) throws EsCircuitedBreakingException {
        List<Long> fails = new ArrayList<>();
        List<Long> successData;
        List<Long> ids = originalData.stream().map(AssetCell::getId).toList();
        List<String> errMsgs = new ArrayList<>();
        if (response.errors()) {
            // 当执行批量删除时，可能会返回404错误，当做删除成功处理
            List<BulkResponseItem> errItems = response.items().stream()
                    .filter(item -> item.status() != 200 && item.status() != 404).toList();
            fails = errItems.stream().map(BulkResponseItem::id).filter(Objects::nonNull).map(Long::valueOf)
                    .collect(Collectors.toList());
            errMsgs = errItems.stream().map(BulkResponseItem::error).filter(Objects::nonNull).map(ErrorCause::toString)
                    .collect(Collectors.toList());
            // 检查是否包含断路器异常
            if (errItems.stream()
                    .anyMatch(bulkResponseItem -> bulkResponseItem.status() == EsCircuitedBreakingException.STATUS)) {
                throw new EsCircuitedBreakingException(String.join(";", errMsgs));
            }
            List<Long> finalFails = fails;
            successData = ids.stream().filter(id -> !finalFails.contains(id)).collect(Collectors.toList());
        } else {
            successData = new ArrayList<>(ids);
        }
        ProcessResult result = new ProcessResult();
        result.setDocCount(originalData.size());
        result.setFails(fails);
        result.setSuccessData(successData);
        if (!errMsgs.isEmpty()) {
            result.setErrorMessage(String.join(";", errMsgs));
        }
        return result;
    }

    private ElasticsearchClient buildClient(EsClusterDTO esCluster) {
        // 节点配置解析
        String protocol = Objects.nonNull(esCluster.getUseSsl()) && esCluster.getUseSsl() ? "https" : "http";
        String[] hosts = esCluster.getHosts().split(",");
        HttpHost[] httpHosts = new HttpHost[hosts.length];
        Arrays.stream(hosts).map(host -> HttpHost.create(String.format("%s://%s", protocol, host)))
                .toList().toArray(httpHosts);

        // 凭据生成
        String password = skynet.boot.security.CryptoUtil.decrypt(esCluster.getSecret(), properties.getEsSecretKey());
        CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(esCluster.getUser(), password));

        // 构建RestClient
        RestClient restClient = RestClient.builder(httpHosts)
                .setHttpClientConfigCallback(httpClientBuilder -> {
                    // 创建信任所有证书的 SSLContext
                    try {
                        SSLContext sslContext = SSLContextBuilder.create()
                                .loadTrustMaterial((chain, authType) -> true) // 信任所有证书
                                .build();
                        // 设置 Basic Authentication,禁用ssl验证
                        return httpClientBuilder.setSSLContext(sslContext)
                                .setDefaultCredentialsProvider(credentialsProvider)
                                .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }).build();

        // 构建传输层
        ElasticsearchTransport transport = new RestClientTransport(
                restClient, new JacksonJsonpMapper());

        // 构建客户端
        return new ElasticsearchClient(transport);
    }

    private RemovalListener<Long, ElasticsearchClient> newRemovalListener() {
        return notification -> {
            try {
                try (ElasticsearchClient value = notification.getValue()) {
                    log.debug("remove ElasticsearchClient : key[{}] , cluster[{}]", notification.getKey(), Objects.requireNonNull(value)
                            .info().clusterName());
                }
            } catch (IOException e) {
                log.error("fail to close ElasticsearchClient", e);
            }
        };
    }

    @Override
    public void close() {
        this.cachedClients.asMap().forEach((k, v) -> {
            try {
                v.close();
            } catch (IOException e) {
                log.error("fail to close ElasticsearchClient.id={}", k, e);
            }
        });
    }

    /**
     * 智能转换Map结构（支持嵌套/非嵌套混合情况）
     *
     * @param flatMap 输入Map可能包含{"key":value}或{"parent.child":value}
     * @return 自动构建的嵌套结构或原样返回
     */
    private Map<String, Object> convert(Map<String, Object> flatMap) {
        if (flatMap == null) return null;
        Map<String, Object> result = new HashMap<>();

        // 处理嵌套逻辑
        for (Map.Entry<String, Object> entry : flatMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (key.contains(".")) {
                String[] path = key.split("\\.");
                Map<String, Object> current = result;

                for (int i = 0; i < path.length - 1; i++) {
                    current = (Map<String, Object>) current.computeIfAbsent(path[i], k -> new HashMap<String, Object>());
                }
                current.put(path[path.length - 1], value);
            } else {
                result.put(key, value);
            }
        }

        return result;
    }
}