package com.iflytek.lynxiao.asset.action.bucket.writer.config;

import com.iflytek.lynxiao.asset.action.bucket.writer.condition.ConditionalOnBucketWriter;
import com.iflytek.lynxiao.asset.action.bucket.writer.service.BucketWriteService;
import com.iflytek.lynxiao.asset.action.bucket.writer.service.BucketWriteServiceImpl;
import com.iflytek.lynxiao.asset.cache.BucketCacheService;
import com.iflytek.lynxiao.common.annotation.EnableDatashard;
import com.iflytek.lynxiao.common.datashard.DatashardEncoder;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 功能描述
 *
 * @author: cwruan
 * @Date: 2025-06-04 10:36
 */
@EnableDatashard
@Configuration(proxyBeanMethods = false)
@ConditionalOnBucketWriter
public class BucketWriteConfiguration {

    @Bean
    @ConfigurationProperties("lynxiao.asset.action.bucket.write")
    public BucketWriteProperties bucketWriteProperties() {
        return new BucketWriteProperties();
    }

    @Bean
    public BucketWriteService bucketWriteService(BucketCacheService bucketCacheService, BucketWriteProperties bucketWriteProperties,
                                                 DatashardEncoder datashardEncoder) {
        return new BucketWriteServiceImpl(bucketCacheService, bucketWriteProperties, datashardEncoder);
    }
}