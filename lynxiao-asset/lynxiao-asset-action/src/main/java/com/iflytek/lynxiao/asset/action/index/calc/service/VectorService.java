package com.iflytek.lynxiao.asset.action.index.calc.service;

import com.iflytek.lynxiao.data.domain.AssetCell;
import com.iflytek.lynxiao.data.dto.featurebase.FeatureBaseConfigDTO;

import java.util.List;

/**
 * @author: leitong
 * @date: 2024/11/7 14:48
 * @description:
 **/
public interface VectorService {

    List<AssetCell> calc(String traceId, String site, List<AssetCell> docs, FeatureBaseConfigDTO featureBaseConfig);

}