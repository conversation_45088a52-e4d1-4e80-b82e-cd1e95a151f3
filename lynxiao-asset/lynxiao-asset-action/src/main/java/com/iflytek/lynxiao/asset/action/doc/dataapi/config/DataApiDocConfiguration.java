package com.iflytek.lynxiao.asset.action.doc.dataapi.config;

import com.iflytek.lynxiao.asset.action.doc.dataapi.condition.ConditionalOnDocDataApi;
import com.iflytek.lynxiao.asset.action.doc.dataapi.service.DataApiDocService;
import com.iflytek.lynxiao.asset.action.doc.dataapi.service.DataApiMedicalService;
import com.iflytek.lynxiao.asset.action.doc.dataapi.service.DataApiSiteService;
import com.iflytek.lynxiao.common.annotation.EnableDatashard;
import com.iflytek.lynxiao.common.dataapi.DataApiRedisService;
import com.iflytek.lynxiao.common.datashard.DatashardDecoder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.data.mongodb.core.MongoTemplate;
import skynet.boot.pandora.ogma.annotation.EnableSkynetPandoraOgma;

/**
 * @author: leitong
 * @date: 2025/6/5 11:36
 * @description:
 **/
@EnableDatashard
@EnableSkynetPandoraOgma
@EnableFeignClients
@Configuration(proxyBeanMethods = false)
@ImportAutoConfiguration(exclude = DataSourceAutoConfiguration.class)
@Import({
        org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration.class,
        org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration.class
})
@ConditionalOnDocDataApi
public class DataApiDocConfiguration {

    @Bean
    @ConfigurationProperties(prefix = "lynxiao.asset.action.doc.dataapi")
    public DataApiDocProperties dataApiDocProperties() {
        return new DataApiDocProperties();
    }


    @Bean
    public DataApiDocService dataApiDocService(DataApiSiteService dataApiSiteService,
                                               DataApiMedicalService dataApiMedicalService,
                                               DatashardDecoder datashardDecoder) {
        return new DataApiDocService(datashardDecoder, dataApiSiteService, dataApiMedicalService);
    }

    @Bean
    public DataApiSiteService dataApiSiteService(DataApiRedisService dataApiRedisService,
                                                 DataApiDocProperties dataApiDocProperties) {
        return new DataApiSiteService(dataApiRedisService, dataApiDocProperties);
    }

    @Bean
    public DataApiMedicalService dataApiMedicalService(@Qualifier("platformMongoTemplate") MongoTemplate platformMongoTemplate) {
        return new DataApiMedicalService(platformMongoTemplate);
    }
}