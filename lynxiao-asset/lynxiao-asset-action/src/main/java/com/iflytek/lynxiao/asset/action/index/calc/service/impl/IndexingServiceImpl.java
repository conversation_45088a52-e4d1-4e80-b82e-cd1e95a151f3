package com.iflytek.lynxiao.asset.action.index.calc.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.asset.action.index.calc.config.IndexCalcProperties;
import com.iflytek.lynxiao.asset.action.index.calc.service.IndexingService;
import com.iflytek.lynxiao.asset.action.index.calc.service.TokenizeService;
import com.iflytek.lynxiao.asset.action.index.calc.service.VectorService;
import com.iflytek.lynxiao.common.datashard.DatashardDecoder;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.common.utils.JsonUtil;
import com.iflytek.lynxiao.data.constant.IndexingErrorType;
import com.iflytek.lynxiao.data.domain.AssetCell;
import com.iflytek.lynxiao.data.dto.action.calc.IndexingDTO;
import com.iflytek.lynxiao.data.dto.featurebase.FeatureBaseConfigDTO;
import com.iflytek.lynxiao.data.utils.ValidateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import skynet.boot.pandora.api.ApiFrameStatus;
import skynet.boot.pandora.api.ApiResponse;
import skynet.boot.pandora.api.ApiResponseHeader;
import skynet.boot.pandora.support.MqSessionContext;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 索引计算
 *
 * @author: cwruan
 * @Date: 2025-06-05 14:44
 */
@Slf4j
public class IndexingServiceImpl implements IndexingService {

    private final DatashardDecoder datashardDecoder;
    private final TokenizeService tokenizeService;
    private final VectorService vectorService;
    private final IndexCalcProperties properties;

    public IndexingServiceImpl(DatashardDecoder datashardDecoder,
                               TokenizeService tokenizeService,
                               VectorService vectorService,
                               IndexCalcProperties properties) {
        this.datashardDecoder = datashardDecoder;
        this.tokenizeService = tokenizeService;
        this.vectorService = vectorService;
        this.properties = properties;
    }

    @Override
    public void process(IndexingDTO data, MqSessionContext sessionContext, String traceId, String taskId) throws Exception {
        // 字段校验
        ValidateUtil.validate(data);
        //  按照源桶分组，如果源通不存在，则按照批次的来源桶作为缓存桶编码
        Map<String, List<AssetCell>> bucketAndCells = data.getDocs().stream()
                .collect(Collectors.groupingBy(cell -> cell.getProps().getSourceBucketCode() == null ? data.getBucketCode() : cell.getProps().getSourceBucketCode()));
        AtomicInteger batchCount = new AtomicInteger(0);
        List<AssetCell> allCells = new ArrayList<>();
        for (String bucketCode : bucketAndCells.keySet()) {
            List<List<AssetCell>> partition = ListUtil.partition(bucketAndCells.get(bucketCode), properties.getBatch());
            for (List<AssetCell> batch : partition) {
                List<AssetCell> indexDataDocs = this.indexing(batch, data.getFeatureBaseConfig(), bucketCode, traceId);
                allCells.addAll(indexDataDocs);
                // 每发一次kafka计数器加1
                batchCount.addAndGet(1);
            }
        }
        ApiResponse apiResponse = new ApiResponse(JSONObject.of("data", allCells));
        ApiResponseHeader header = apiResponse.getHeader();
        header.put("status", ApiFrameStatus.ALL_FRAME.ordinal());
        apiResponse.setHeader(header);
        sessionContext.send(apiResponse);
    }


    private List<AssetCell> indexing(List<AssetCell> batch, FeatureBaseConfigDTO featureBaseConfig, String bucketCode, String traceId) {
        // 通过正文缓存服务获取原始数据
        batch = decodeCells(batch);

        // 构建id到返回doc的映射
        Map<String, AssetCell> idCellMap = batch.stream().collect(Collectors.toMap(AssetCell::getIdAsString, cell -> cell));
        // 分词计算
        tokenize(batch, featureBaseConfig, traceId, idCellMap);
        // 向量计算
        embedding(batch, featureBaseConfig, bucketCode, traceId, idCellMap);

        return new ArrayList<>(idCellMap.values());
    }


    private void embedding(List<AssetCell> batch, FeatureBaseConfigDTO featureBaseConfig, String bucketCode,
                           String traceId, Map<String, AssetCell> idCellMap) {
        if (Objects.isNull(featureBaseConfig.getVectorConfig())) {
            return;
        }

        long vectorStart = System.currentTimeMillis();
        try {
            List<AssetCell> vectorCellList = this.vectorService.calc(traceId, bucketCode, batch, featureBaseConfig);
            vectorCellList.forEach(vectorCell -> {
                // 将向量计算结果合并到文档中
                String id = vectorCell.getIdAsString();
                Map<String, Object> mergedResult = JsonUtil.merge(idCellMap.get(id), vectorCell);
                idCellMap.put(id, AssetCell.from(mergedResult));
            });

        } catch (Exception e) {
            if (e instanceof LynxiaoException) {
                throw e;
            }
            throw new LynxiaoException(IndexingErrorType.VECTOR_ERROR.getCode(), e.getMessage());
        }
        log.debug("vector cost {}ms", System.currentTimeMillis() - vectorStart);
    }

    private void tokenize(List<AssetCell> batch, FeatureBaseConfigDTO featureBaseConfig, String traceId, Map<String, AssetCell> idCellMap) {
        // 按照配置进行计算
        if (Objects.isNull(featureBaseConfig.getTokenizationConfig())) {
            return;
        }
        long tokenizationStart = System.currentTimeMillis();
        // 如果分词配置存在，进行分词计算
        try {
            List<Map<String, Object>> tokenizationList = this.tokenizeService.calc(traceId, batch, featureBaseConfig);
            tokenizationList.forEach(tokenization -> {
                // 将分词计算结果合并到文档中
                String id = tokenization.get("_id").toString();
                Map<String, Object> restoredResult = JsonUtil.restore(tokenization);
                Map<String, Object> mergedResult = JsonUtil.merge(idCellMap.get(id), restoredResult);
                idCellMap.put(id, AssetCell.from(mergedResult));
            });
        } catch (Exception e) {
            throw new LynxiaoException(IndexingErrorType.TOKENIZE_ERROR.getCode(), e.getMessage());
        }
        log.debug("tokenization cost = {}ms", System.currentTimeMillis() - tokenizationStart);
    }


    private List<AssetCell> decodeCells(List<AssetCell> batch) {
        // 通过正文缓存服务获取原始数据
        long datashardStart = System.currentTimeMillis();
        try {
            List<Map<String, Object>> decode = datashardDecoder.decode(batch.stream().map(AssetCell::toMap).toList());
            batch = decode.stream().map(doc -> {
                AssetCell cell = AssetCell.from(doc);
                if (StringUtils.isBlank(cell.getContent())) {
                    throw new LynxiaoException(IndexingErrorType.CONTENT_NOT_PRESENT.getCode(), "doc content is empty. id:".concat(cell.getId().toString()));
                }
                return cell;
            }).toList();

        } catch (Exception e) {
            log.error("get content from datashard failed. e:", e);
            throw new LynxiaoException(IndexingErrorType.DATASHARD_ERROR.getCode(), "get content from datashard failed.".concat(e.getMessage()));
        }
        log.debug("datashard cost = {}ms", System.currentTimeMillis() - datashardStart);
        return batch;
    }

}