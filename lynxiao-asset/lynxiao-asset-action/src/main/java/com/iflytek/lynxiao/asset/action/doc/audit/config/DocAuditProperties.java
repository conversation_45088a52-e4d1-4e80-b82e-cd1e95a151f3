package com.iflytek.lynxiao.asset.action.doc.audit.config;

import lombok.Getter;
import lombok.Setter;

/**
 * 功能描述
 *
 * @author: cwruan
 * @Date: 2025-08-04 19:05
 */
@Getter
@Setter
public class DocAuditProperties {

    private boolean enabled;

    // 审批每批数量
    private int auditBatch = 500;

    /**
     * 线程池配置
     */
    private DocAuditProperties.ThreadPoolConfig threadPoolConfig;


    /**
     * 数据审核线程池配置
     */
    private DocAuditProperties.ThreadPoolConfig updateThreadPoolConfig;

    @Getter
    @Setter
    public static class ThreadPoolConfig {
        private int corePoolSize;
        private int maxPoolSize;
        private int queueCapacity;
    }
}