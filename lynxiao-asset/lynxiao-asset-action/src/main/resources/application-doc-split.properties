#------------------------------------------------------------------------------------------------------
# Basic Settings
#------------------------------------------------------------------------------------------------------
server.port=30222
spring.application.name=lynxiao-asset-doc-split
#-----------------------------------------------service-------------------------------------------------
lynxiao.asset.action.doc.split.enabled=true
lynxiao.asset.action.doc.split.batch-size=100
#-----------------------------------------------pandora-------------------------------------------------
skynet.pandora.mq.endpoint.doc-split.enabled=true
skynet.pandora.mq.endpoint.doc-split.handler-size=8
skynet.pandora.mq.consumer-topics.lynxiao-asset-doc-split.enabled=true
skynet.pandora.mq.consumer-topics.lynxiao-asset-doc-split.consumer-size=8
skynet.pandora.mq.consumer-topics.lynxiao-asset-doc-split.handle-endpoint=doc-split
skynet.pandora.mq.consumer-topics.lynxiao-asset-doc-split-debug.enabled=true
skynet.pandora.mq.consumer-topics.lynxiao-asset-doc-split-debug.consumer-size=2
skynet.pandora.mq.consumer-topics.lynxiao-asset-doc-split-debug.handle-endpoint=doc-split
#-----------------------------------------------snowflake-------------------------------------------------
lynxiao.snowflake.enabled=true
