#------------------------------------------------------------------------------------------------------
# Basic Settings
#------------------------------------------------------------------------------------------------------
server.port=30211
spring.application.name=lynxiao-asset-bucket-read
#-----------------------------------------------service------------------------------------------------
lynxiao.asset.action.bucket.read.enabled=true
lynxiao.asset.action.bucket.read.thread-pool-config.core-pool-size=100
lynxiao.asset.action.bucket.read.thread-pool-config.max-pool-size=100
lynxiao.asset.action.bucket.read.thread-pool-config.queue-capacity=1000

# asynchronous send thread pool configuration
lynxiao.asset.action.bucket.read.async-send-thread-pool-config.core-pool-size=50
lynxiao.asset.action.bucket.read.async-send-thread-pool-config.max-pool-size=50
lynxiao.asset.action.bucket.read.async-send-thread-pool-config.queue-capacity=1000
lynxiao.asset.action.bucket.read.async-send-thread-pool-config.keep-alive-seconds=60
#-----------------------------------------------pandora------------------------------------------------
skynet.pandora.mq.consumer-topics.lynxiao-asset-bucket-read.enabled=true
skynet.pandora.mq.consumer-topics.lynxiao-asset-bucket-read.consumer-size=8
skynet.pandora.mq.consumer-topics.lynxiao-asset-bucket-read.handle-endpoint=bucket-read
skynet.pandora.mq.consumer-topics.lynxiao-asset-bucket-read-debug.enabled=true
skynet.pandora.mq.consumer-topics.lynxiao-asset-bucket-read-debug.consumer-size=2
skynet.pandora.mq.consumer-topics.lynxiao-asset-bucket-read-debug.handle-endpoint=bucket-read
skynet.pandora.mq.endpoint.bucket-read.enabled=true
skynet.pandora.mq.endpoint.bucket-read.handler-size=8
#-----------------------------------------------mongo--------------------------------------------------
lynxiao.mongo.enabled=true
lynxiao.platform-mongo.enabled=true
lynxiao.platform-mongo.uri=mongodb+srv://${lynxiao.platform.mongodb.user}:${lynxiao.platform.mongodb.password}@${lynxiao.platform.mongodb.host}/lynxiao_platform?authSource=admin&tls=false&ssl=false
spring.data.mongodb.uri=${lynxiao.platform-mongo.uri}