#------------------------------------------------------------------------------------------------------
# Lynxiao Feature Configuration
#------------------------------------------------------------------------------------------------------
lynxiao.asset.action.doc.dataapi.enabled=true
#------------------------------------------------------------------------------------------------------
# Endpoint Configuration
skynet.pandora.mq.endpoint.dataapi.enabled=true
skynet.pandora.mq.endpoint.dataapi.handler-size=32
skynet.pandora.mq.endpoint.dataapi.handle-timeout=5m
# Consumer Topics Configuration
skynet.pandora.mq.consumer-topics.lynxiao-asset-doc-dataapi.enabled=${lynxiao.asset.action.doc.dataapi.enabled}
skynet.pandora.mq.consumer-topics.lynxiao-asset-doc-dataapi.consumer-size=8
skynet.pandora.mq.consumer-topics.lynxiao-asset-doc-dataapi.handle-endpoint=dataapi
skynet.pandora.mq.consumer-topics.lynxiao-asset-doc-dataapi.group=${SKYNET_PANDORA_MQ_CONSUMER_GROUP}
skynet.pandora.mq.consumer-topics.lynxiao-asset-doc-dataapi-debug.enabled=${lynxiao.asset.action.doc.dataapi.enabled}
skynet.pandora.mq.consumer-topics.lynxiao-asset-doc-dataapi-debug.consumer-size=2
skynet.pandora.mq.consumer-topics.lynxiao-asset-doc-dataapi-debug.handle-endpoint=dataapi
#------------------------------------------------------------------------------------------------------
# Mongo Configuration
#------------------------------------------------------------------------------------------------------
lynxiao.mongo.enabled=true
lynxiao.platform-mongo.enabled=true
lynxiao.platform-mongo.uri=mongodb+srv://${lynxiao.platform.mongodb.user}:${lynxiao.platform.mongodb.password}@${lynxiao.platform.mongodb.host}/lynxiao_platform?authSource=admin&tls=false&ssl=false
spring.data.mongodb.uri=${lynxiao.platform-mongo.uri}
#------------------------------------------------------------------------------------------------------
# Redis Configuration
#------------------------------------------------------------------------------------------------------
spring.data.redis.cluster.nodes=${turing.cloud.paas.redis.nodes}
spring.data.redis.password=${turing.cloud.paas.redis.password}
spring.data.redis.timeout=5s
spring.data.redis.lettuce.pool.max-active=256
spring.data.redis.lettuce.pool.max-idle=50
spring.data.redis.lettuce.pool.min-idle=10
spring.data.redis.lettuce.pool.max-wait=10s
spring.data.redis.lettuce.cluster.refresh.adaptive=true
spring.data.redis.lettuce.cluster.refresh.period=5s
spring.data.redis.lettuce.shutdown-timeout=100ms