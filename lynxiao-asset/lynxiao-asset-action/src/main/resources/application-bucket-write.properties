#------------------------------------------------------------------------------------------------------
# Basic Settings
#------------------------------------------------------------------------------------------------------
server.port=30212
spring.application.name=lynxiao-asset-bucket-write
#-----------------------------------------------service-------------------------------------------------
lynxiao.asset.action.bucket.write.enabled=true
lynxiao.asset.action.bucket.write.batch-size=10
lynxiao.asset.action.bucket.write.thread-pool-config.core-pool-size=100
lynxiao.asset.action.bucket.write.thread-pool-config.max-pool-size=100
lynxiao.asset.action.bucket.write.thread-pool-config.queue-capacity=1000
#-----------------------------------------------pandora-------------------------------------------------
skynet.pandora.mq.consumer-topics.lynxiao-asset-bucket-write.enabled=true
skynet.pandora.mq.consumer-topics.lynxiao-asset-bucket-write.consumer-size=8
skynet.pandora.mq.consumer-topics.lynxiao-asset-bucket-write.handle-endpoint=bucket-write
skynet.pandora.mq.consumer-topics.lynxiao-asset-bucket-write-debug.enabled=true
skynet.pandora.mq.consumer-topics.lynxiao-asset-bucket-write-debug.consumer-size=2
skynet.pandora.mq.consumer-topics.lynxiao-asset-bucket-write-debug.handle-endpoint=bucket-write
skynet.pandora.mq.endpoint.bucket-write.enabled=true
skynet.pandora.mq.endpoint.bucket-write.handler-size=8