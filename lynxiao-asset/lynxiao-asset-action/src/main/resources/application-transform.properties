spring.application.name=lynxiao-asset-cell-transform
lynxiao.asset.action.doc.transform.enabled=true
#------------------------------------------------------------------------------------------------------
# Message Queue Configuration
#------------------------------------------------------------------------------------------------------
# Endpoint Configuration
skynet.pandora.mq.endpoint.transform.enabled=true
skynet.pandora.mq.endpoint.transform.handler-size=32
skynet.pandora.mq.endpoint.transform.handle-timeout=15m
# Consumer Topics Configuration
skynet.pandora.mq.consumer-topics.lynxiao-asset-cell-transform.enabled=${skynet.pandora.mq.endpoint.transform.enabled}
skynet.pandora.mq.consumer-topics.lynxiao-asset-cell-transform.consumer-size=8
skynet.pandora.mq.consumer-topics.lynxiao-asset-cell-transform.handle-endpoint=transform
skynet.pandora.mq.consumer-topics.lynxiao-asset-cell-transform.group=${SKYNET_PANDORA_MQ_CONSUMER_GROUP}
skynet.pandora.mq.consumer-topics.lynxiao-asset-cell-transform-debug.enabled=${skynet.pandora.mq.endpoint.transform.enabled}
skynet.pandora.mq.consumer-topics.lynxiao-asset-cell-transform-debug.consumer-size=2
skynet.pandora.mq.consumer-topics.lynxiao-asset-cell-transform-debug.handle-endpoint=transform
#logging.level.org.springframework.boot.autoconfigure=debug