#------------------------------------------------------------------------------------------------------
# Basic Settings
#------------------------------------------------------------------------------------------------------
server.port=30213
spring.application.name=lynxiao-asset-doc-audit
#-----------------------------------------------service------------------------------------------------
lynxiao.asset.action.doc.audit.enabled=true
lynxiao.asset.action.doc.audit.thread-pool-config.core-pool-size=50
lynxiao.asset.action.doc.audit.thread-pool-config.max-pool-size=50
lynxiao.asset.action.doc.audit.thread-pool-config.queue-capacity=1000
#-----------------------------------------------pandora------------------------------------------------
skynet.pandora.mq.endpoint.doc-audit.enabled=true
skynet.pandora.mq.endpoint.doc-audit.handler-size=8
#-----------------------------------------------mongo--------------------------------------------------
lynxiao.mongo.enabled=true
lynxiao.feature-mongo.enabled=true
lynxiao.feature-mongo.uri=mongodb+srv://${lynxiao.index.mongodb.user}:${lynxiao.index.mongodb.password}@${lynxiao.index.mongodb.host}/lynxiao_feature?authSource=admin&tls=false&ssl=false
spring.data.mongodb.uri=${lynxiao.feature-mongo.uri}