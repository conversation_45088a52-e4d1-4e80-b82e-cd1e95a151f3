#------------------------------------------------------------------------------------------------------
# Basic Settings
#------------------------------------------------------------------------------------------------------
server.port=30221
spring.application.name=lynxiao-asset-doc-parse
#-----------------------------------------------service-------------------------------------------------
lynxiao.asset.action.doc.parse.enabled=true
lynxiao.asset.action.doc.parse.server-name=doc-convert-v2
lynxiao.asset.action.doc.parse.server-path=/api/mineru/v1/parse_file
#-----------------------------------------------pandora-------------------------------------------------
skynet.pandora.mq.endpoint.doc-parse.enabled=true
skynet.pandora.mq.endpoint.doc-parse.handler-size=8
skynet.pandora.mq.consumer-topics.lynxiao-asset-doc-parse.enabled=true
skynet.pandora.mq.consumer-topics.lynxiao-asset-doc-parse.consumer-size=8
skynet.pandora.mq.consumer-topics.lynxiao-asset-doc-parse.handle-endpoint=doc-parse
skynet.pandora.mq.consumer-topics.lynxiao-asset-doc-parse-debug.enabled=true
skynet.pandora.mq.consumer-topics.lynxiao-asset-doc-parse-debug.consumer-size=2
skynet.pandora.mq.consumer-topics.lynxiao-asset-doc-parse-debug.handle-endpoint=doc-parse
