#------------------------------------------------------------------------------------------------------
# Basic Settings
#------------------------------------------------------------------------------------------------------
spring.application.name=lynxiao-asset-action
spring.main.allow-bean-definition-overriding=true
management.health.elasticsearch.enabled=false
management.health.redis.enabled=false
#------------------------------------------------------------------------------------------------------
# Skynet TLB Configuration
#------------------------------------------------------------------------------------------------------
skynet.tlb.enabled=true
skynet.tlb.endpoints=
spring.cloud.tlb.discovery.default-service-tag-selector=
spring.cloud.tlb.discovery.register=false
#------------------------------------------------kafka-------------------------------------------------
spring.kafka.bootstrap-servers=
spring.kafka.properties.request.timeout.ms=300000
# 生产者内存缓冲区设置128M
spring.kafka.producer.properties.buffer-memory=134217728
spring.kafka.producer.properties.max-block-ms=120000
#------------------------------------------------------------------------------------------------------
# Skynet Pandora Configuration
#------------------------------------------------------------------------------------------------------
skynet.pandora.enabled=true
skynet.pandora.default-handler-size=64
skynet.pandora.func.enabled=false
skynet.pandora.talk.enabled=true
skynet.pandora.talk.timeout=1800s
skynet.pandora.ogma.enabled=true
skynet.pandora.ogma.timeout=60s
skynet.pandora.ogma.mq.enabled=true
skynet.pandora.ogma.mq.mq-uri=${skynet.pandora.mq.mq-uri}
skynet.pandora.ogma.default-service-tag-selector=${spring.cloud.tlb.discovery.default-service-tag-selector}
#------------------------------------------------------------------------------------------------------
skynet.pandora.brave.enabled=true
skynet.pandora.brave.aop-enabled=true
skynet.pandora.brave.file-enabled=false
skynet.pandora.brave.allow-subject-codes=00
skynet.pandora.brave.ellipses-enabled=true
skynet.pandora.brave.ellipses-array-limit-len=128
skynet.pandora.brave.ellipses-string-limit-len=100
skynet.pandora.brave.kafka.enabled=true
skynet.pandora.brave.kafka.topic-prefix=
skynet.pandora.brave.kafka.topic-expression=lynxiao_flow
skynet.pandora.brave.kafka.config.bootstrap-servers=${lynxiao.elk.kafka.servers}
#------------------------------------------------------------------------------------------------------
skynet.pandora.mq.enabled=true
skynet.pandora.mq.mq-uri=${spring.kafka.bootstrap-servers}
skynet.pandora.mq.connected-timeout=10
# ?????????
skynet.pandora.mq.cancel-options.enabled=true
skynet.pandora.mq.cancel-options.topic=lynxiao_asset_task_ctl
skynet.pandora.mq.cancel-options.taskIdHeaderKey=PANDORA-TASK-ID
skynet.pandora.mq.cancel-options.cacheMaxTaskSize=256
skynet.pandora.mq.cancel-options.cacheTtl=12h
#------------------------------------------------------------------------------------------------------
# Caching Configuration
#------------------------------------------------------------------------------------------------------
spring.cache.type=caffeine
spring.cache.cache-names=featureBaseInfo
spring.cache.caffeine.spec=maximumSize=256,expireAfterWrite=60s
#-----------------------------------------------service-------------------------------------------------
lynxiao.asset.action.cache-ttl=30s
lynxiao.asset.portal.cache.enabled=true
#--------------------------------------------------  mongo----------------------------------------------------
lynxiao.mongo.enabled=false
lynxiao.label-mongo.enabled=false
lynxiao.doc-mongo.enabled=false
lynxiao.daily-mongo.enabled=false
lynxiao.platform-mongo.enabled=false
lynxiao.dataset-mongo.enabled=false
lynxiao.feature-mongo.enabled=false
#------------------------- Redis Configuration -------------------------
spring.data.redis.cluster.nodes=${turing.cloud.paas.redis.nodes}
spring.data.redis.password=${turing.cloud.paas.redis.password}
spring.data.redis.lettuce.pool.max-active=100
spring.data.redis.lettuce.pool.max-idle=10
spring.data.redis.lettuce.pool.min-idle=5
spring.data.redis.lettuce.pool.max-wait=5s
#-------------------------------------------------log---------------------------------------------------
logging.pattern.console=%d{MM-dd HH:mm:ss.SSS} %clr(%5p) %clr([%9t]){faint} %clr(%-40.40logger{39}[%3line{3}][%X{SKYNET_CURRENT_TRACE_ID}][task:%X{taskId}]){cyan}%clr(:){faint} %m%n