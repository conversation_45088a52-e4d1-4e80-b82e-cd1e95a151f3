#------------------------------------------------------------------------------------------------------
# Lynxiao Feature Configuration
#------------------------------------------------------------------------------------------------------
lynxiao.asset.action.index.sink.enabled=true
lynxiao.mongo.enabled=true
lynxiao.feature-mongo.enabled=true
#------------------------------------------------------------------------------------------------------
# MongoDB Configuration
lynxiao.feature-mongo.uri=mongodb+srv://${lynxiao.index.mongodb.user}:${lynxiao.index.mongodb.password}@${lynxiao.index.mongodb.host}/lynxiao_feature?authSource=admin&tls=false&ssl=false
spring.data.mongodb.uri=${lynxiao.feature-mongo.uri}
spring.data.mongodb.auto-index-creation=true
#------------------------------------------------------------------------------------------------------
# Message Queue Configuration
#------------------------------------------------------------------------------------------------------
# Endpoint Configuration
skynet.pandora.mq.endpoint.sink.enabled=true
skynet.pandora.mq.endpoint.sink.handler-size=32
skynet.pandora.mq.endpoint.sink.handle-timeout=15m
