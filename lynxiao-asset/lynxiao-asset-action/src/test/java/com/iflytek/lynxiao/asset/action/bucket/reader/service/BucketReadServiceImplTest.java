package com.iflytek.lynxiao.asset.action.bucket.reader.service;

import com.iflytek.lynxiao.asset.action.bucket.reader.config.BucketReadProperties;
import com.iflytek.lynxiao.asset.cache.BucketCacheService;
import com.iflytek.lynxiao.resource.repository.AssetTaskBatchRepository;
import io.micrometer.core.instrument.MeterRegistry;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import skynet.boot.pandora.ogma.PandoraApiRequestObserverBuilder;
import skynet.boot.pandora.support.TaskCancelCache;
import skynet.boot.pandora.brave.TraceUtils;

import java.lang.reflect.Field;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * BucketReadServiceImpl测试类，重点测试线程池队列满时的阻塞等待逻辑
 */
class BucketReadServiceImplTest {

    @Mock
    private PandoraApiRequestObserverBuilder apiRequestObserverBuilder;
    
    @Mock
    private BucketCacheService bucketCacheService;
    
    @Mock
    private AssetTaskBatchRepository assetTaskBatchRepository;
    
    @Mock
    private TaskCancelCache taskCancelCache;
    
    @Mock
    private TraceUtils traceUtils;
    
    @Mock
    private MeterRegistry meterRegistry;

    private BucketReadServiceImpl bucketReadService;
    
    private AutoCloseable closeable;

    @BeforeEach
    void setUp() {
        closeable = MockitoAnnotations.openMocks(this);
        
        // 创建小容量线程池配置，便于测试队列满的情况
        BucketReadProperties bucketReadProperties = createSmallThreadPoolConfig();
        
        bucketReadService = new BucketReadServiceImpl(
            apiRequestObserverBuilder,
            bucketCacheService, 
            bucketReadProperties,
            assetTaskBatchRepository,
            taskCancelCache,
            traceUtils,
            meterRegistry
        );
    }

    @AfterEach
    void tearDown() throws Exception {
        if (closeable != null) {
            closeable.close();
        }
        // 关闭线程池
        if (bucketReadService != null) {
            ExecutorService executor = getAsyncSendExecutor();
            if (executor != null && !executor.isShutdown()) {
                executor.shutdown();
                executor.awaitTermination(5, TimeUnit.SECONDS);
            }
        }
    }

    /**
     * 测试线程池队列满时的阻塞等待逻辑
     * 
     * 改进的测试场景：
     * 1. 创建小容量线程池（1个核心线程，1个最大线程，队列容量2）
     * 2. 提交长时间运行的任务填满线程池和队列
     * 3. 在独立线程中提交新任务，验证会阻塞等待
     * 4. 释放一个任务后，验证阻塞的任务能成功提交并执行
     */
    @Test
    void testThreadPoolQueueFullBlocking() throws Exception {
        ExecutorService executor = getAsyncSendExecutor();
        assertNotNull(executor, "异步发送线程池不应为null");
        assertTrue(executor instanceof ThreadPoolExecutor, "应该是ThreadPoolExecutor实例");
        
        ThreadPoolExecutor threadPoolExecutor = (ThreadPoolExecutor) executor;
        
        // 验证线程池配置
        assertEquals(1, threadPoolExecutor.getCorePoolSize(), "核心线程数应该为1");
        assertEquals(1, threadPoolExecutor.getMaximumPoolSize(), "最大线程数应该为1");
        assertEquals(2, threadPoolExecutor.getQueue().remainingCapacity() + threadPoolExecutor.getQueue().size(), 
                    "队列总容量应该为2");

        // 用于精确控制任务执行时机的门闩
        CountDownLatch taskStartedLatch = new CountDownLatch(1); // 控制第一个任务开始执行
        CountDownLatch releaseTaskLatch = new CountDownLatch(1); // 控制释放第一个任务
        
        AtomicInteger executedTaskCount = new AtomicInteger(0);
        AtomicInteger submittedTaskCount = new AtomicInteger(0);

        // 创建长时间阻塞的任务（用于占用线程池）
        Runnable longRunningTask = () -> {
            try {
                taskStartedLatch.countDown(); // 标记任务开始
                executedTaskCount.incrementAndGet();
                System.out.println("长时间任务开始执行，线程：" + Thread.currentThread().getName());
                releaseTaskLatch.await(); // 等待释放信号
                Thread.sleep(2000); // 模拟任务处理时间
                System.out.println("长时间任务执行完成，线程：" + Thread.currentThread().getName());
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                System.out.println("长时间任务被中断，线程：" + Thread.currentThread().getName());
            }
        };

        // 创建普通任务
        Runnable normalTask = () -> {
            executedTaskCount.incrementAndGet();
            try {
                Thread.sleep(1000); // 模拟任务处理时间
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            System.out.println("普通任务执行，线程：" + Thread.currentThread().getName());
        };

        System.out.println("=== 开始测试线程池队列满阻塞场景 ===");
        
        // 第1步：提交第一个长时间运行的任务（占用唯一的工作线程）
        threadPoolExecutor.execute(longRunningTask);
        submittedTaskCount.incrementAndGet();
        System.out.println("已提交第1个长时间任务");
        
        // 等待第一个任务开始执行
        assertTrue(taskStartedLatch.await(2, TimeUnit.SECONDS), "第一个任务应该开始执行");
        
        // 第2步：提交两个普通任务填满队列
        threadPoolExecutor.execute(normalTask);
        threadPoolExecutor.execute(normalTask);
        submittedTaskCount.addAndGet(2);
        System.out.println("已提交2个普通任务到队列，队列应该已满");
        
        // 验证队列状态
        Thread.sleep(1000); // 给系统一点时间处理
        assertEquals(0, threadPoolExecutor.getQueue().remainingCapacity(), "队列应该已满");
        assertEquals(1, threadPoolExecutor.getActiveCount(), "应该有1个活跃线程");
        assertEquals(2, threadPoolExecutor.getQueue().size(), "队列中应该有2个等待任务");

        // 第3步：在独立线程中提交第4个任务，这应该会阻塞
        CountDownLatch blockingSubmissionStarted = new CountDownLatch(1);
        CountDownLatch blockingSubmissionCompleted = new CountDownLatch(1);
        
        Thread submissionThread = new Thread(() -> {
            try {
                blockingSubmissionStarted.countDown();
                System.out.println("开始提交第4个任务（应该会阻塞）...");
                long startTime = System.currentTimeMillis();
                
                // 这个调用应该会阻塞等待，直到队列有空间
                threadPoolExecutor.execute(normalTask);
                
                long endTime = System.currentTimeMillis();
                System.out.println("第4个任务提交成功！阻塞时间：" + (endTime - startTime) + "ms");
                submittedTaskCount.incrementAndGet();
                blockingSubmissionCompleted.countDown();
            } catch (Exception e) {
                System.err.println("提交第4个任务时发生异常：" + e.getMessage());
                e.printStackTrace();
            }
        }, "task-submission-thread");

        submissionThread.start();
        
        // 第4步：验证第4个任务确实在阻塞
        assertTrue(blockingSubmissionStarted.await(1, TimeUnit.SECONDS), "提交线程应该开始");
        Thread.sleep(200); // 等待足够时间验证阻塞
        assertEquals(1, blockingSubmissionCompleted.getCount(), "第4个任务应该还在阻塞中");
        System.out.println("验证：第4个任务确实在阻塞等待队列空间");

        // 第5步：释放第一个长时间任务，为队列腾出空间
        System.out.println("释放第一个长时间任务...");
        releaseTaskLatch.countDown();

        // 第6步：验证阻塞的任务能够成功提交
        assertTrue(blockingSubmissionCompleted.await(3, TimeUnit.SECONDS), "阻塞的任务应该能成功提交");
        System.out.println("验证：阻塞的任务已成功提交");

        // 等待提交线程结束
        submissionThread.join(2000);
        
        // 等待所有任务执行完成
        long timeout = 5000;
        long startWait = System.currentTimeMillis();
        while (executedTaskCount.get() < 4 && (System.currentTimeMillis() - startWait) < timeout) {
            Thread.sleep(50);
        }

        // 最终验证
        assertEquals(4, submittedTaskCount.get(), "应该成功提交4个任务");
        assertEquals(4, executedTaskCount.get(), "所有4个任务都应该被执行");
        
        System.out.println("=== 测试完成：队列满阻塞等待机制工作正常 ===");
    }

    /**
     * 测试线程池关闭时的拒绝策略
     */
    @Test
    void testRejectedExecutionWhenShutdown() throws Exception {
        ExecutorService executor = getAsyncSendExecutor();
        ThreadPoolExecutor threadPoolExecutor = (ThreadPoolExecutor) executor;
        
        // 关闭线程池
        threadPoolExecutor.shutdown();
        
        // 提交任务应该抛出RejectedExecutionException
        assertThrows(Exception.class, () -> {
            threadPoolExecutor.execute(() -> {
                // 这个任务不应该被执行
            });
        });
    }

    /**
     * 测试被中断时的处理逻辑
     */
    @Test
    void testInterruptedExceptionHandling() throws Exception {
        ExecutorService executor = getAsyncSendExecutor();
        ThreadPoolExecutor threadPoolExecutor = (ThreadPoolExecutor) executor;
        
        // 填满线程池和队列
        CountDownLatch blockLatch = new CountDownLatch(1);
        
        // 提交任务填满线程池和队列
        for (int i = 0; i < 3; i++) {
            threadPoolExecutor.execute(() -> {
                try {
                    blockLatch.await();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }
        
        Thread.sleep(50); // 确保队列已满

        // 在新线程中提交任务并中断
        AtomicInteger exceptionCount = new AtomicInteger(0);
        Thread submissionThread = new Thread(() -> {
            try {
                threadPoolExecutor.execute(() -> {});
            } catch (Exception e) {
                exceptionCount.incrementAndGet();
                assertTrue(Thread.currentThread().isInterrupted(), "线程应该被标记为中断状态");
            }
        });

        submissionThread.start();
        Thread.sleep(100); // 等待开始阻塞
        submissionThread.interrupt(); // 中断提交线程
        submissionThread.join();

        assertEquals(1, exceptionCount.get(), "应该捕获到一个异常");
        
        // 清理
        blockLatch.countDown();
    }

    /**
     * 创建小容量线程池配置用于测试
     */
    private BucketReadProperties createSmallThreadPoolConfig() {
        BucketReadProperties properties = mock(BucketReadProperties.class);
        BucketReadProperties.AsyncSendThreadPoolConfig asyncConfig = 
            new BucketReadProperties.AsyncSendThreadPoolConfig();
        
        // 设置小容量配置便于测试
        asyncConfig.setCorePoolSize(1);
        asyncConfig.setMaxPoolSize(1);
        asyncConfig.setQueueCapacity(2);
        asyncConfig.setKeepAliveSeconds(60);
        
        when(properties.getAsyncSendThreadPoolConfig()).thenReturn(asyncConfig);
        return properties;
    }

    /**
     * 通过反射获取异步发送线程池
     */
    private ExecutorService getAsyncSendExecutor() throws Exception {
        Field field = BucketReadServiceImpl.class.getDeclaredField("asyncSendExecutor");
        field.setAccessible(true);
        return (ExecutorService) field.get(bucketReadService);
    }
}