package com.iflytek.lynxiao.asset.action.index.calc.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.msgpack.jackson.dataformat.MessagePackMapper;
import skynet.boot.pandora.api.ApiRequest;

import java.io.IOException;

/**
 * <AUTHOR>  2025/8/21 16:32
 */
@Slf4j
public class MsgpackTest {

    public static void main(String[] args) throws IOException {
        float[] vector = new float[]{-0.016815726f,0.05905659f,0.014753608f,0.013119067f,0.0050117997f,-0.02386409f,-0.008235097f,-0.07305066f,0.0149283465f,-0.018890848f,0.008760671f,-0.020670686f,-0.035600457f,-0.03685346f,0.049979124f,-0.114557795f,0.001983118f,0.0031003891f,-0.016565107f,0.0044015427f,0.014601322f,-0.016909143f,0.031344816f,0.05782064f,-0.0058705467f,-0.050167155f,0.055239532f,0.0073421653f,0.068057105f,0.027079787f,0.021191254f,-0.004841379f,-0.017722413f,0.01026049f,-0.0021574472f,0.005683747f,0.030122671f,0.027240325f,-0.045134787f,-0.01518758f,-0.02832415f,0.0042987554f,0.0443513f,-0.044036034f,-0.0068946965f,0.054061327f,0.011536438f,0.0601322f,0.018435441f,0.0031492251f,0.015160491f,-0.061280217f,0.009777696f,0.0072536846f,0.039387707f,0.017372617f,-0.04599751f,-0.016585119f,0.058359273f,-0.09139316f,0.022374881f,0.13670065f,-0.05794375f,-0.033633966f,0.033403296f,-0.0085418625f,0.017111572f,0.01652584f,0.0017937408f,0.011293077f,-0.0040850886f,0.07154895f,-0.024601776f,0.08183998f,0.022951955f,0.06230639f,0.08046985f,-0.05024295f,-0.013916488f,-0.038422544f,0.00904616f,0.04025248f,0.06098652f,-0.036658138f,-0.0098882895f,-0.018260688f,-0.017739868f,0.022486266f,-0.028746966f,-0.04589389f,0.028689131f,-0.014263563f,0.068105504f,-0.027612332f,-0.05194983f,0.0026299867f,0.016595153f,0.010881443f,0.014830781f,-0.023008395f,-0.013671531f,0.0014683591f,-0.010959248f,-0.006612229f,-0.010426444f,0.007333013f,0.017828515f,-0.007503971f,0.055394176f,0.02784278f,-0.028957056f,0.01779151f,-0.010564733f,0.048795976f,-0.0457869f,0.023929833f,-0.012940588f,-0.03676395f,-0.09463883f,-0.030613523f,0.004346338f,0.022029046f,-0.0066867494f,-0.0055020344f,-0.063779816f,0.088123634f,0.015796578f,0.04322762f,0.037707638f,0.0034547462f,0.038034305f,-0.0012769016f,-0.0052900338f,-0.07932922f,0.02344005f,-0.005313836f,0.01823283f,0.023954228f,-0.009291527f,-0.07258882f,-0.05368177f,-0.03230937f,-0.010062068f,0.026946202f,0.016008118f,-0.006948433f,-0.044540394f,-0.0023673503f,-0.01507989f,0.03965098f,-0.07501721f,0.024652692f,0.020993145f,0.0075656576f,0.019003484f,0.029053984f,0.0064462507f,0.009289061f,-0.016588492f,-0.021929866f,0.023430094f,-0.01904869f,-0.04843624f,0.043556582f,-0.060127676f,0.033821627f,0.02050603f,0.01636201f,0.018000377f,-0.0019781305f,-0.0591829f,0.0111540435f,-0.005124651f,0.02871921f,-0.02950325f,-0.07771221f,0.024584074f,0.011814138f,0.0005660666f,-0.017274113f,0.042125855f,-0.033769377f,0.003565089f,-0.033176728f,-0.03147074f,0.024848878f,0.048609287f,0.019002428f,0.06360816f,0.036871206f,0.012380533f,-0.062517054f,-0.065414295f,0.061246324f,0.009989623f,0.046179887f,-0.00013205611f,0.005029156f,0.024934674f,-0.019200766f,-0.03073016f,0.023624627f,-0.01664732f,-0.013010334f,0.002216847f,-0.049666032f,-0.0077661444f,-0.017088389f,-0.028136505f,-0.033546958f,0.018688967f,-0.0050677313f,0.006364626f,-0.027762316f,-0.038731128f,0.04539433f,0.026713707f,-0.013762699f,0.033313036f,0.0054095266f,-0.008349512f,-0.032604482f,-0.010170489f,-0.0560296f,0.022150455f,-0.014686781f,-0.02434436f,-0.0007419673f,-0.014286108f,-0.033272427f,0.03725062f,-0.043820567f,-0.014638611f,-0.045702465f,0.0014839434f,-0.028744709f,-0.04868548f,-0.028190043f,-0.056669045f,0.054086424f,0.020328024f,-0.07085664f,0.061345737f,0.022663558f,0.06979985f,0.0078449305f,-0.028539103f,0.03009719f,-0.0009304383f,0.017792897f,-0.032631006f,-0.01026053f,-0.035002254f,0.0076829293f,0.027844843f,-0.051258504f,0.016042296f,0.026033603f,-0.036132008f,0.050819553f,0.054338284f,0.028519949f,-0.009838991f,-0.020640174f,-0.003895862f,-0.09214391f,-0.026740074f,-0.0030033519f,0.028112305f,0.026260152f,-0.038944617f,0.059618827f,0.07581514f,-0.017674819f,-0.002999336f,0.010950062f,0.032682862f,-0.016793229f,-0.013362692f,0.0015036887f,0.029912157f,0.015562365f,0.016850805f,0.008710273f,-0.03698358f,-0.0067821653f,0.05737292f,0.017423457f,0.0011148977f,0.0028176508f,-0.05173361f,-0.07487669f,-0.03068602f,0.05005764f,0.037422527f,0.0109180845f,-0.018664986f,-0.0404395f,-0.028646387f,-0.00026535577f,-0.0059013157f,-0.017541802f,0.030391868f,0.057511654f,-0.04005908f,0.00022876606f,0.0117120715f,-0.0458086f,-0.04987854f,0.041634455f,-0.01067376f,-0.06558137f,-0.029440075f,-0.020960946f,0.032063138f,-0.050971195f,-0.050738577f,0.002278833f,-0.010433338f,0.057564642f,0.0035397054f,-0.004032822f,0.035996318f,0.040101003f,-0.014388607f,0.04749351f,0.029332645f,0.012585823f,-0.08358189f,-0.0033914438f,0.04501635f,0.016613787f,-0.0058688563f,0.03078769f,-0.00090559514f,0.06903206f,-0.013969805f,-0.03025497f,0.025947886f,-0.0033324833f,-0.030252224f,-0.005523525f,-0.058469985f,-0.023190366f,0.0036555335f,-0.019443443f,-0.09719f,0.008495122f,-0.010350275f,0.04671885f,0.033179786f,-0.0014022144f,0.00062251923f,0.049581137f,0.031779133f,0.0075570745f,-0.01625948f,-0.020527132f,-0.02858099f,0.05049279f,0.050803363f,-0.016873104f,0.033772703f,0.016386976f,-0.003676429f,-0.0016448034f,-0.035209104f,-0.010348737f,0.0075150933f,0.021653479f,0.008755779f,0.025690762f,-0.010571839f,-0.030737123f,-0.04303634f,-0.029030202f,0.042466648f,-0.029332193f,-0.0029088538f,-0.03967229f,-0.15630656f,0.0033351223f,-0.05446178f,0.09145983f,0.022783717f,-0.0064213253f,-0.03836506f,-0.023160264f,-0.043139223f,-0.042220995f,-0.05133746f,-0.06372183f,-0.0055063157f,0.0017054898f,0.0068481844f,-0.06611194f,0.023016192f,0.032634325f,-0.032091092f,-0.018768108f,0.015587691f,-0.019418884f,0.022816865f,0.012076369f,0.005212373f,0.034882046f,0.033541933f,-0.017525245f,0.0106456755f,-0.045340102f,-0.021071436f,-0.015464917f,-0.0019632473f,-0.047070652f,0.020341268f,-0.044050053f,0.050841607f,0.026055759f,-0.040989697f,0.0047967983f,0.041197453f,0.017512167f,-0.028534504f,-0.052275307f,0.059664376f,0.013093326f,0.018244036f,0.010424337f,0.071323104f,-0.008915233f,0.025752146f,0.0019554514f,0.006789163f,0.0083519975f,-0.0011256333f,0.009069087f,0.06282195f,0.0055237287f,-0.023451697f,-0.09375087f,0.008004587f,0.10725599f,0.0050383788f,-0.011559171f,0.04069953f,-0.033301327f,0.016774416f,-0.014690832f,0.013905245f,-0.007930811f,-0.037604634f,-0.013242705f,0.025189543f,-0.0027173192f,0.008944305f,-0.04618394f,-0.020003106f,-0.03057972f,0.04110708f,0.010791412f,0.001975402f,-0.04310022f,-0.015313251f,0.037011325f,0.025007658f,0.037563104f,0.027398154f,-0.02547136f,0.04149532f,0.0045957705f,0.022326559f,-0.0130018825f,0.00044658635f,0.015516172f,0.03264981f,0.0314273f,0.003661289f,0.005642936f,0.011506559f,-0.07704465f,0.031147275f,0.0102123f,0.02150408f,-0.05326229f,-0.0056915106f,0.016145112f,-0.031518504f,-0.017172867f,-0.03016693f,-0.031216988f,0.0012309783f,-0.048895188f,-0.02285824f,-0.020141654f,-0.041196827f,0.0409665f,0.015592926f,-0.01898278f,0.060474116f,-0.017187677f,0.02605565f,0.011127765f,0.063583255f,-0.045490053f,0.042645928f,-0.031217087f,-0.0026246703f,-0.0017027581f,-0.024966037f,-0.006437499f,-0.07032834f,-0.016596366f,0.013877669f,0.029145036f,-0.01978047f,0.020298172f,0.007380293f,0.036462497f,-0.042449486f,-0.04208971f,-0.01651624f,0.0017507689f,-0.03788412f,0.050160155f,0.025678378f,-0.024589924f,-0.031020164f,-0.015644753f,0.0028262783f,0.037914883f,0.019322291f,0.05951702f,-0.01024864f,-0.0090229185f,0.049952086f,0.061604228f,0.062872514f,0.042251818f,-0.024156956f,0.042282026f,-0.010521184f,-0.019601459f,0.005553239f,-0.043553796f,-0.050466165f,-0.045131538f,0.056170274f,-0.0063666357f,-0.01737201f,-0.023716684f,-0.035920866f,0.0003953121f,-0.012089836f,0.07811294f,0.002664686f,-0.022258926f,0.032182395f,0.01832308f,0.0028155856f,-0.009063447f,-0.04001551f,0.067218706f,0.033974987f,-0.005411913f,0.063396685f,-0.0021532564f,-0.00035336582f,0.06343837f,-0.0098515125f,0.087602384f,0.014702338f,-0.04146212f,-0.026378954f,-0.07793537f,-0.017368386f,0.027085934f,0.016547091f,0.041749552f,-0.061122254f,0.029674402f,-0.054845393f,0.021492185f,0.008570827f,0.0013541557f,-0.042058364f,-0.034665965f,-0.07342894f,0.004284892f,0.040003646f,-0.027483331f,-0.034800526f,-0.019610787f,0.055879943f,-0.044129226f,0.032278944f,0.0042856066f,0.02553402f,0.023377536f,-0.035336208f,0.0043416256f,0.060005154f,0.0045617837f,0.05104812f,0.022313781f,-0.042716824f,0.046174094f,-0.009518027f,-0.022384416f,-0.03084114f,0.005008623f,-0.010240336f,-0.003535263f,0.014339647f,0.0151230525f,0.03163995f,0.026073338f,-0.015531803f,-0.02012244f,0.03577766f,-0.044662658f,-0.001930766f,-0.013609149f,-0.0007189837f,-0.08770878f,0.021337278f,0.016252637f,-0.0054712566f,0.033918537f,-0.062347393f,0.020011345f,0.021606846f,0.00836539f,0.022214891f,-0.002641157f,0.009863289f,-0.062948965f,-0.06712658f,-0.033539303f,0.0064216494f,-0.019057263f,0.078635685f,0.002415908f,-0.019050736f,0.07820103f,-0.0072594043f,0.06200281f,0.06703354f,-0.055143196f,-0.018341219f,0.043382846f,-0.019444155f,-0.061968107f,0.017072195f,-0.026418606f,0.060135633f,-0.000044743036f,-0.003751287f,0.006759731f,0.060165755f,-0.012950293f,-0.002212794f,-0.029399013f,0.006057689f,-0.05665605f,-0.030635362f,0.051869914f,-0.005181879f,-0.024210073f,0.08137399f,0.03858885f,0.036215663f,0.095700294f,0.021678759f,-0.018025856f,-0.0048807156f,0.025986629f,0.013385216f,0.009884797f,0.0026624366f,0.024780745f,-0.045600902f,-0.0022556114f,-0.018328194f,0.016810318f,-0.007568803f,-0.012162892f,0.02686826f,-0.010164309f,-0.015417671f,-0.006019849f,0.035083406f,-0.0035998626f,-0.007510491f,0.024287723f,0.018500976f,0.007187622f,-0.004296021f,-0.036148112f,-0.013579164f,0.004208377f,0.04092429f,-0.004926413f,0.04487308f,0.005847942f,-0.047727704f,-0.041831598f,-0.011830129f,0.014339905f,-0.05547024f,0.02970602f,-0.0038200757f,0.012105889f,0.009066023f,0.03279447f,0.041852865f,-0.05889217f,-0.005432179f,-0.021309942f,-0.013456047f,0.10207394f,-0.036326643f,-0.016963921f,-0.0030448239f,-0.0022425486f,-0.045680374f,0.02325056f,0.037867337f,0.032960504f,0.086720236f,0.02017383f,0.09105981f,0.053423245f,0.007974966f,0.016428279f,-0.042127743f,-0.058646817f,-0.029157097f,0.015512538f,0.013878906f,-0.02101109f,0.014097263f,0.020867376f,0.018393831f,0.06851275f,-0.0032219538f,0.019839486f,-0.06798995f,-0.01001712f,-0.02464723f,-0.021176668f,0.032797832f,-0.03617339f,-0.06766195f,0.07762681f,0.0035642867f,0.033047292f,0.03221687f,-0.0205102f,0.021764986f,-0.0161905f,-0.041823547f,-0.0073303008f,0.032965828f,-0.020318199f,-0.015931308f,0.047224097f,-0.03617558f,-0.04107173f};

        JSONObject payload = new JSONObject();
        payload.put("vector", vector);
        ApiRequest apiRequest = new ApiRequest();
        apiRequest.setTraceId("traceId-1234567890");
        apiRequest.setPayload(payload);

        ObjectMapper objectMapper = new MessagePackMapper();
        byte[] serialized = objectMapper.writeValueAsBytes(apiRequest);
        ApiRequest deserialized = objectMapper.readValue(serialized, ApiRequest.class);
        log.info("Deserialized apiRequest = {}", deserialized);
        Vector vector1 = apiRequest.getPayload().to(Vector.class);
        log.info("Deserialized vector = {}", vector1.getVector());

    }

    @Setter
    @Getter
    static class Vector {
        public float[] vector;
    }
}
