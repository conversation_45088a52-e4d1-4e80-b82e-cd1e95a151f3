package com.iflytek.lynxiao.asset.action.index.calc.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.asset.ActionApp;
import com.iflytek.lynxiao.asset.action.index.calc.service.VectorService;
import com.iflytek.lynxiao.data.domain.AssetCell;
import com.iflytek.lynxiao.data.dto.asset.IdxBucketExtConfig;
import com.iflytek.lynxiao.data.dto.featurebase.FeatureBaseConfigDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

/**
 * <AUTHOR>  2025/8/18 14:54
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = ActionApp.class)
@ActiveProfiles({"dev", "calc"})
class VectorServiceImplTest {

    @Autowired
    private VectorService vectorService;

    @Test
    void testProcessWithRealBeans() {
        // 准备测试数据
        String config = """
                {"clusterId":"1","dsBucketCode":"YLSSINOC_SET_BFL7GVMX","dsBucketName":"医疗期刊库0616","idInRegion":"656","idxDbCode":"IDXSBOGQL","idxDbConfig":{"additionalProperties":["readableContent"],"encryptFields":[],"encrypted":false,"fields":[{"field":"_id","type":"long"},{"field":"domain","type":"string"},{"field":"s","type":"int"},{"field":"post_ts","type":"long"}],"splitConfig":{"configs":{},"field":"ss","splitField":"content","type":""},"tokenizationConfig":{"fields":[{"calcField":"title","defaultWeight":10.0,"featureField":"title_terms","maxLen":512,"service":"ai-seg-com-v2"},{"calcField":"sub_title","defaultWeight":10.0,"featureField":"sub_title_terms","maxLen":512,"service":"ai-seg-com-v2"},{"calcField":"ss[*].content","defaultWeight":5.0,"featureField":"ss[*].content_terms","maxLen":512,"service":"ai-seg-com-v2"}]},"tokenizationConfigEnabled":true,"vectorConfig":{"fields":[{"calcFields":[{"name":"title","concatString":""},{"name":"sub_title","concatString":"\\\\u005F"},{"name":"ss[*].content","concatString":"\\\\u000A"}],"dim":768,"efConstruction":256,"featureField":"embedding","m":32,"quantization":"bbq","service":"ai-embedding-medical-v2-2","similarity":"l2_norm"}]},"vectorConfigEnabled":true,"version":"20250610"},"region":"hf"}
                """;

        String docsStr = """
                [
                {
                                    "_id": "1952721226954833924",
                                    "_x": {
                                        "uts": 1754916466004,
                                        "cts": 1754399994969,
                                        "cat": "2025-08-11T12:47:46.004799460Z",
                                        "df": 0,
                                        "v": 0,
                                        "au": "System",
                                        "at": 1754916466004,
                                        "a": 1,
                                        "op": 1,
                                        "sbc": "YLSSINOC_WEB_JJSODKIZ"
                                    },
                                    "category": "个案报告",
                                    "chunk_id": 1,
                                    "content": "刘 凯 吴 琪 魏进旺 张 鑫 樊友武 张治元\\n关键词\\n【关键词】血管畸形；头皮；治疗\\n【文章编号】1009-153X(2013)02-0126-02\\n【文献标志码】B\\n【中国图书资料分类号】R 654.4头皮血管畸形是一类发生于颈外血管系统的血管畸形，其发病率低，临床治疗复杂，术后易复发。我科收治2例复杂的头皮血管畸形患者，1例栓塞后行外科手术治疗，1例单纯手术切除，均取得了良好的效果。\\n1 病例资料 病例1,女性，19岁，因发现左顶部头皮包块6年，破溃、出血、感染5月入院。查体：神志清，顶枕部可见一约6cm×6 cm包块，局部破溃、流脓，并形成较多脓苔，包块搏动明显。 头颅MRI示顶枕部及右侧小脑占位，T_1、T_2像均为低信号留空影，考虑头皮下及右侧小脑血管畸形。脑血管造影检查示顶枕部头皮下动静脉畸形，多支颈外血管供血，畸形血管团与颅内血管无沟通；右侧小脑弥散状崎形血管影（图1）。先局\\n个案报告。 14.0 7.17\\n1.07 D10cm <graph1> MRI图\\n血管造影\\n<graph2>\\n1020\\n麻下行头皮血管栓塞术。栓塞完毕后造影显示畸形血管团明显减少，流量明显降低；查体示头皮包块明显减小，渗出减少，搏动减弱。术后1周感染控制后，局麻下行颞顶部两侧头皮扩张器一期埋植术；2月后，全麻下行头皮残余血管畸形病灶切除术。术后切口愈合良好，头发生长正常，效果满意。\\n病例2,男性，36岁，因发现头部进行性增大包块7年人院。查体：神志清，右侧颞顶部一约12cm×5cm包块，头皮无破溃，包块质地中等，可推动，可触及搏动。头颅CT示右颞顶部头皮软组织包块，颅骨无破坏。MRI示右颞顶部头皮软组织包块，T_1、T_2像均可见低信号留空影。脑血管造影检查示右颞顶部头皮下动静脉瘘，双侧多支颈外血管供血，与颅内血管无沟通（图2）。全麻下行头皮动静脉瘘切除术。术后切口愈合良好，头皮无坏死，头发生长正常。\\n2 讨 论 头皮血管畸形为头颈部血管畸形中特殊的一类，较为罕见；多表现为搏动性包块、头皮出血、感染及坏死，甚至有报道巨大血管畸形导致心肌肥大、高血压及心功能衰竭。病例1为青年女性，畸形团于青春期进行性增大，后出现破溃、出血及感染。例2为中年男性，主要表现为进行性增大的搏动性头皮包块。头颅CT、MRI及多普勒超声为头皮血管畸形的重要检查方法，但现今脑血管造影仍为其诊断的金标准。颈内、颈外动脉的造影可以明确血管畸形的供血动脉、引流静脉、病变的范围以及与颅内血管有无沟通。本文2例患者 MRI均示为高流量血管畸形，造影结果都为颈外动脉多支供血。有学者认为颅内的血管畸形可引起上矢状窦的压力增高，进而诱发头皮的血管畸形。病例1除头皮的血管畸形外，尚并存右侧小脑的血管畸形。\\n头皮血管畸形主要存在美容的问题，在治疗方式选择上往往需要综合考虑。手术切除、介入及硬化剂注射为主要的治疗方法。手术切除为基础的治疗方法，但存在术中大出血、切除不全术后易复发及术后头皮缺损、感染等风险。介入方法已广泛应用于血管畸形的治疗，但单纯栓塞仅对部分病例有效。硬化剂注射要求设备齐全，操作者经验丰富，否则易发生血管栓塞、皮肤坏死等并发症。\\n病例1病变存在感染创口，且头皮有缺损，为手术切除禁忌症。因此，我们通过介入治疗栓塞大部分供血动脉，减少创面渗出。待控制创面感染后，二期手术有效解决术后头皮缺损或疤痕形成而影响美容的问题，效果满意。栓塞强调超选择，尽量接近病灶，从病灶内最小的血管开始由内到外栓塞，从而最大可能地避免其他供血动脉迅速扩张，为下一步手术切除减小创伤，既能较好地控制和消除病灶，又能最大程度的保留原有的功能和外观，是比较理想的治疗手段。\\n病例2,我们选择传统的手术切除，效果满意。需注意的是：皮瓣应足够大，包含整个病变；仔细辨认正常血管和畸形血管，正常血管应尽量保留，以免术后头皮缺血坏死，而畸形的供血动脉则必须完全彻底地切除，避免术后复发；分离过程中注意保留完整的真皮层，可有效减少术中出血和防止术后头皮缺损。\\n(2012-06-18收稿，2012-07-05修回)",
                                    "gid": "2d8462e71b99ae57d2019faa4d79022a43acaf37b8ab74fc8783496eac4ac8e3",
                                    "journal_name": "中国临床神经外科杂志",
                                    "len": 1688,
                                    "post_ts": 1356998400,
                                    "ss": [
                                        {
                                            "content": "刘 凯 吴 琪 魏进旺 张 鑫 樊友武 张治元\\n关键词\\n【关键词】血管畸形；头皮；治疗\\n【文章编号】1009-153X(2013)02-0126-02\\n【文献标志码】B\\n【中国图书资料分类号】R 654.4头皮血管畸形是一类发生于颈外血管系统的血管畸形，其发病率低，临床治疗复杂，术后易复发。我科收治2例复杂的头皮血管畸形患者，1例栓塞后行外科手术治疗，1例单纯手术切除，均取得了良好的效果。\\n1 病例资料 病例1,女性，19岁，因发现左顶部头皮包块6年，破溃、出血、感染5月入院。查体：神志清，顶枕部可见一约6cm×6 cm包块，局部破溃、流脓，并形成较多脓苔，包块搏动明显。 头颅MRI示顶枕部及右侧小脑占位，T_1、T_2像均为低信号留空影，考虑头皮下及右侧小脑血管畸形。脑血管造影检查示顶枕部头皮下动静脉畸形，多支颈外血管供血，畸形血管团与颅内血管无沟通；右侧小脑弥散状崎形血管影（图1）。先局\\n个案报告。 14.0 7.17\\n1.07 D10cm <graph1> MRI图\\n血管造影\\n<graph2>\\n1020\\n麻下行头皮血管栓塞术。栓塞完毕后造影显示畸形血管团明显减少，流量明显降低；查体示头皮包块明显减小，渗出减少，搏动减弱。术后1周感染控制后，局麻下行颞顶部两侧头皮扩张器一期埋植术；2月后，全麻下行头皮残余血管畸形病灶切除术。术后切口愈合良好，头发生长正常，效果满意。\\n病例2,男性，36岁，因发现头部进行性增大包块7年人院。查体：神志清，右侧颞顶部一约12cm×5cm包块，头皮无破溃，包块质地中等，可推动，可触及搏动。头颅CT示右颞顶部头皮软组织包块，颅骨无破坏。MRI示右颞顶部头皮软组织包块，T_1、T_2像均可见低信号留空影。脑血管造影检查示右颞顶部头皮下动静脉瘘，双侧多支颈外血管供血，与颅内血管无沟通（图2）。全麻下行头皮动静脉瘘切除术。术后切口愈合良好，头皮无坏死，头发生长正常。\\n2 讨 论 头皮血管畸形为头颈部血管畸形中特殊的一类，较为罕见；多表现为搏动性包块、头皮出血、感染及坏死，甚至有报道巨大血管畸形导致心肌肥大、高血压及心功能衰竭。病例1为青年女性，畸形团于青春期进行性增大，后出现破溃、出血及感染。例2为中年男性，主要表现为进行性增大的搏动性头皮包块。头颅CT、MRI及多普勒超声为头皮血管畸形的重要检查方法，但现今脑血管造影仍为其诊断的金标准。颈内、颈外动脉的造影可以明确血管畸形的供血动脉、引流静脉、病变的范围以及与颅内血管有无沟通。本文2例患者 MRI均示为高流量血管畸形，造影结果都为颈外动脉多支供血。有学者认为颅内的血管畸形可引起上矢状窦的压力增高，进而诱发头皮的血管畸形。病例1除头皮的血管畸形外，尚并存右侧小脑的血管畸形。\\n头皮血管畸形主要存在美容的问题，在治疗方式选择上往往需要综合考虑。手术切除、介入及硬化剂注射为主要的治疗方法。手术切除为基础的治疗方法，但存在术中大出血、切除不全术后易复发及术后头皮缺损、感染等风险。介入方法已广泛应用于血管畸形的治疗，但单纯栓塞仅对部分病例有效。硬化剂注射要求设备齐全，操作者经验丰富，否则易发生血管栓塞、皮肤坏死等并发症。\\n病例1病变存在感染创口，且头皮有缺损，为手术切除禁忌症。因此，我们通过介入治疗栓塞大部分供血动脉，减少创面渗出。待控制创面感染后，二期手术有效解决术后头皮缺损或疤痕形成而影响美容的问题，效果满意。栓塞强调超选择，尽量接近病灶，从病灶内最小的血管开始由内到外栓塞，从而最大可能地避免其他供血动脉迅速扩张，为下一步手术切除减小创伤，既能较好地控制和消除病灶，又能最大程度的保留原有的功能和外观，是比较理想的治疗手段。\\n病例2,我们选择传统的手术切除，效果满意。需注意的是：皮瓣应足够大，包含整个病变；仔细辨认正常血管和畸形血管，正常血管应尽量保留，以免术后头皮缺血坏死，而畸形的供血动脉则必须完全彻底地切除，避免术后复发；分离过程中注意保留完整的真皮层，可有效减少术中出血和防止术后头皮缺损。\\n(2012-06-18收稿，2012-07-05修回)",
                                            "ctx": {
                                                "span": [
                                                    0,
                                                    1688
                                                ]
                                            }
                                        }
                                    ],
                                    "title": "头皮血管畸形2例报告",
                                    "sub_title": "子标题",
                                    "unique_id": "cnki_ZGLC201302030",
                                    "url": "https://kns.cnki.net/kcms2/article/abstract?v=Oa1N_PzK0nTezG_fDkGzULS-Uo9O-UKCG2NC4QpbSoi8ojZQlh63kAp7rzzkqIbtf2aIEbH-nYzAK88gOux95WIBziRQGtfeBOeRL9-siHAB-VTBLmLqKOLQdDHcaz_mSagbs3LC1eAe18tVOpskU_X9mzfbiY6dCGuVnKVNEOKQqFGGnjbaxF45EguN-aoG&uniplatform=NZKPT&language=CHS",
                                    "url_web": "https://wap.cnki.net/touch/web/Journal/Article/ZGLC201302030.html"
                                }
                              ]
                """;

        List<AssetCell> docs = JSONArray.parseArray(docsStr, AssetCell.class);
        IdxBucketExtConfig idxBucketExtConfig = JSONObject.parseObject(config, IdxBucketExtConfig.class);
        FeatureBaseConfigDTO featureBaseConfig = new FeatureBaseConfigDTO();
        featureBaseConfig.setVectorConfig(idxBucketExtConfig.getIdxDbConfig().getVectorConfig());


        List<AssetCell> calc = vectorService.calc(
                "traceId",
                "site",
                docs,
                featureBaseConfig
        );

        System.out.println("向量计算结果：" + JSONObject.toJSONString(calc));
    }

}