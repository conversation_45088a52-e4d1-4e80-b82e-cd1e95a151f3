package com.iflytek.lynxiao.asset.action.doc.transform;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.data.domain.AssetCell;
import com.iflytek.lynxiao.data.dto.action.transform.CellTransformInput;
import org.apache.commons.io.IOUtils;
import org.springframework.core.io.ClassPathResource;
import skynet.boot.script.CodeExecutor;
import skynet.boot.script.CodeExecutorBuilder;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>  2025/6/17 14:27
 */
class SplitGroovyTest {

    public static void main(String[] args) throws Exception {
        CodeExecutor codeExecutor = new CodeExecutorBuilder("groovy").build();

        // 构造测试数据
        String docString1 = """
                {
                  "_id": {
                    "$numberLong": "4068610545028522747"
                  },
                  "_x": {
                    "uts": {
                      "$numberLong": "1754969057536"
                    },
                    "cts": {
                      "$numberLong": "1754402732842"
                    },
                    "cat": "2025-08-12T03:24:17.536350335Z",
                    "df": 0,
                    "v": 0,
                    "au": "System",
                    "at": {
                      "$numberLong": "1754969057536"
                    },
                    "a": 1,
                    "op": 1,
                    "sbc": "YLSSINOC_WEB_LFVZ2LIC"
                  },
                  "classify": [
                    "工业产品",
                    "仪器设备"
                  ],
                  "content": "　",
                  "gid": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855",
                  "lemma_id": 1143895,
                  "len": 1,
                  "levels": {
                    "L06": 3
                  },
                  "post_ts": 1655952461,
                  "ss": [
                    {
                      "content": "　",
                      "ctx": {
                        "span": [
                          0,
                          1
                        ]
                      }
                    }
                  ],
                  "title": "轴承加热器 技术参数",
                  "url": "https://baike.baidu.com/item/%E8%BD%B4%E6%89%BF%E5%8A%A0%E7%83%AD%E5%99%A8/1143895#4"
                  }
                """;


        AssetCell cell1 = AssetCell.from(JSONObject.parseObject(docString1));
        cell1.setId(3138032107450698444L);

        List<AssetCell> inputData = Arrays.asList(cell1);

        String script = IOUtils.toString(new ClassPathResource("groovy/med_test_len.groovy").getInputStream(), StandardCharsets.UTF_8);

        CellTransformInput input = new CellTransformInput();
        CellTransformInput.ScriptParam scriptParam = new CellTransformInput.ScriptParam();
        scriptParam.setScript(script);
        scriptParam.setMethod("execute");
        input.setScriptParam(scriptParam);
        input.setData(inputData);

        // 执行转换
        List<AssetCell> result = (List<AssetCell>) codeExecutor.evalScript(input.getScriptParam().getScript(), input.getScriptParam().getMethod(), input.getData());

        // 打印结果
//        System.out.println("转换前数据:");
//        System.out.println(JSONObject.toJSONString(inputData));
        System.out.println("\n转换后数据:");
        System.out.println(JSONObject.toJSONString(result));

        String content = "　";
        System.out.println(content.trim().replaceAll("\\s+", "").length());

    }

}