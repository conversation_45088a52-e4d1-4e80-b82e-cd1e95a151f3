import org.apache.commons.codec.binary.Hex
import javax.crypto.Cipher
import javax.crypto.SecretKey
import javax.crypto.spec.GCMParameterSpec
import javax.crypto.spec.SecretKeySpec
import java.nio.charset.StandardCharsets
import java.security.MessageDigest

class GidUtil {
    private static final Set<Character> CHINESE_PUNCTUATION = Set.of(
            '–', '—', '‘', '’', '“', '”', '…', '、', '。', '〈', '〉',
            '《', '》', '「', '」', '『', '』', '【', '】', '〔', '〕',
            '！', '（', '）', '，', '．', '：', '；', '？'
    )

    static String generateGid(String data) {
        StringBuilder cleaned = new StringBuilder()
        for (char c : data.toCharArray()) {
            if (!isPunctuation(c) && !isWhiteSpace(c) && !isControl(c) && !isChinesePunctuation(c)) {
                cleaned.append(c)
            }
        }

        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256")
            byte[] hashBytes = digest.digest(cleaned.toString().getBytes(StandardCharsets.UTF_8))
            return Hex.encodeHexString(hashBytes)
        } catch (Exception e) {
            throw new RuntimeException("SHA-256 calculation failed", e)
        }
    }

    private static boolean isPunctuation(char c) {
        int cp = c
        if ((cp >= 33 && cp <= 47) || (cp >= 58 && cp <= 64) || (cp >= 91 && cp <= 96) || (cp >= 123 && cp <= 126)) {
            return true
        }
        int type = Character.getType(c)
        switch (type) {
            case Character.CONNECTOR_PUNCTUATION:
            case Character.DASH_PUNCTUATION:
            case Character.END_PUNCTUATION:
            case Character.FINAL_QUOTE_PUNCTUATION:
            case Character.INITIAL_QUOTE_PUNCTUATION:
            case Character.OTHER_PUNCTUATION:
            case Character.START_PUNCTUATION:
                return true
            default:
                return false
        }
    }

    private static boolean isWhiteSpace(char c) {
        return c == ' ' || c == '\t' || c == '\n' || c == '\r' ||
                Character.getType(c) == Character.SPACE_SEPARATOR
    }

    private static boolean isControl(char c) {
        if (c in ['\t', '\n', '\r']) return false
        int type = Character.getType(c)
        switch (type) {
            case Character.CONTROL:
            case Character.DIRECTIONALITY_COMMON_NUMBER_SEPARATOR:
            case Character.FORMAT:
            case Character.PRIVATE_USE:
            case Character.SURROGATE:
            case Character.UNASSIGNED:
                return true
            default:
                return false
        }
    }

    private static boolean isChinesePunctuation(char c) {
        return CHINESE_PUNCTUATION.contains(c)
    }
}

// 主处理函数
static List execute(List docJsonList) {
    docJsonList.collect { doc ->
        def decryptedContent = doc.content ?: ""
        def decryptedTitle = doc.title ?: ""
        def decryptedSummary = doc.summary ?: ""
        def contentLength = decryptedContent.length()

        def newDoc = [:] + doc
        newDoc.content = decryptedContent
        newDoc.title = decryptedTitle
        newDoc.summary = decryptedSummary

        if (!doc.ss || doc.ss.isEmpty()) {
            newDoc.ss = [[
                                 content: decryptedContent,
                                 ctx    : [span: [0, contentLength]]
                         ]]
        } else {
            newDoc.ss = doc.ss.collect { s ->
                def span = s.ctx?.span ?: s.span
                if (span && span.size() == 2 && span[0] < contentLength) {
                    def start = span[0]
                    def end = Math.min(span[1], contentLength)
                    return [
                            content: decryptedContent.substring(start, end),
                            ctx    : [span: span]
                    ]
                } else {
                    return null
                }
            }.findAll { it != null }
        }

        newDoc.gid = GidUtil.generateGid(decryptedContent)
        newDoc.len = decryptedContent.trim().length() > 0 && !decryptedContent.isAllWhitespace() ? decryptedContent.trim().length() : 0
//        newDoc.len = decryptedContent.trim().length()

        // 定义需要保留的字段列表
        def fieldList = ['_id', '_x', 'title', 'content', 'url', 'levels', 'post_ts', 'lemma_id', 'classify', 'redirect_from', 'gid', 'ss','len']

        def filteredDoc = [:]
        fieldList.each { fieldName ->
            filteredDoc[fieldName] = newDoc[fieldName] != null ? newDoc[fieldName] : null
        }
        return filteredDoc
    }
}