{"domainInfos": [{"domainEnName": "asset_ingest_bucket_record", "domainCnName": "数据接入记录", "domainApiExclude": "*", "columnInfos": [{"columnEnName": "datasource_id", "columnCnName": "数据源id", "columnType": "bigint"}, {"columnEnName": "bucket_code", "columnCnName": "桶编码", "columnType": "<PERSON><PERSON><PERSON>"}, {"columnEnName": "path", "columnCnName": "数据路径", "columnType": "<PERSON><PERSON><PERSON>", "columnLength": "1024"}, {"columnEnName": "match_count", "columnCnName": "符合规则数", "columnType": "bigint"}, {"columnEnName": "error_count", "columnCnName": "错误数", "columnType": "bigint"}, {"columnEnName": "status", "columnCnName": "状态", "columnType": "int"}, {"columnEnName": "start_time", "columnCnName": "开始时间", "columnType": "datetime"}, {"columnEnName": "end_time", "columnCnName": "结束时间", "columnType": "datetime"}, {"columnEnName": "deleted", "columnCnName": "删除标记", "columnType": "tinyint"}, {"columnEnName": "rule_md5", "columnCnName": "规则md5", "columnType": "<PERSON><PERSON><PERSON>", "columnLength": "32"}, {"columnEnName": "processed_files", "columnCnName": "已处理文件数", "columnType": "int"}, {"columnEnName": "total_files", "columnCnName": "总文件数", "columnType": "int"}]}]}