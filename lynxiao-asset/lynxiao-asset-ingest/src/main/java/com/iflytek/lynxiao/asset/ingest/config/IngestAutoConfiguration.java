package com.iflytek.lynxiao.asset.ingest.config;

import com.iflytek.lynxiao.asset.ingest.core.*;
import com.iflytek.lynxiao.asset.ingest.repo.BucketRecordRepository;
import com.iflytek.lynxiao.asset.ingest.repo.IngestRecordRepository;
import com.iflytek.lynxiao.common.annotation.EnableDatashard;
import com.iflytek.lynxiao.common.cache.AssetPortalCacheService;
import com.iflytek.lynxiao.common.config.RedisCacheConfiguration;
import com.iflytek.lynxiao.common.datashard.DatashardEncoder;
import com.iflytek.lynxiao.common.snowflake.SnowflakeIdGenerator;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;
import skynet.boot.pandora.annotation.EnableSkynetPandora;
import skynet.boot.pandora.ogma.annotation.EnableSkynetPandoraOgma;
import skynet.boot.pandora.support.MqConsumerProxy;
import skynet.boot.pandora.support.config.MqProperties;

/**
 * @author: leitong
 * @date: 2025/5/28 15:57
 * @description: 数据接入模块配置
 **/
@Slf4j
@EnableDatashard
@EnableFeignClients
@EnableSkynetPandora
@EnableSkynetPandoraOgma
@Configuration(proxyBeanMethods = false)
@AutoConfigureAfter(RedisCacheConfiguration.class)
public class IngestAutoConfiguration {

    @Bean
    @ConfigurationProperties(prefix = "lynxiao.asset.ingest")
    public IngestProperties ingestProperties() {
        return new IngestProperties();
    }

    @Bean
    public RuleManager ruleManager(ApplicationContext applicationContext, IngestProperties properties,
                                   AssetPortalCacheService cacheService) {
        return new RuleManager(applicationContext, properties, cacheService);
    }

    @Bean
    public BucketStorageService bucketStorageService(IngestProperties properties, MongoMappingContext mongoMappingContext,
                                                     MeterRegistry meterRegistry, AssetPortalCacheService assetPortalCacheService) {
        return new BucketStorageService(properties, mongoMappingContext, meterRegistry, assetPortalCacheService);
    }

    @Bean
    public DataHandler dataHandler(IngestProperties properties, RuleManager ruleManager, BucketStorageService bucketStorageService,
                                   DatashardEncoder datashardEncoder, MeterRegistry meterRegistry,
                                   SnowflakeIdGenerator snowflakeIdGenerator) {
        return new DataHandler(properties, ruleManager, bucketStorageService, datashardEncoder, meterRegistry, snowflakeIdGenerator);
    }

    @Bean
    public MqIngesterManager mqIngestManager(ObjectProvider<MqIngester> mqIngesterProvider, IngestProperties properties) {
        return new MqIngesterManager(mqIngesterProvider, properties);
    }

    @Bean
    public FSIngesterManager fsIngestManager(ObjectProvider<FileSystemIngester> objectProvider, IngestProperties properties) {
        return new FSIngesterManager(objectProvider, properties);
    }

    @Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
    @Bean
    public MqIngester mqIngester(MqProperties mqProperties, MqConsumerProxy mqConsumerProxy, DataHandler dataHandler) throws Exception {
        log.trace("create new MqIngester");
        return new MqIngester(mqProperties, mqConsumerProxy, dataHandler);
    }

    @Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
    @Bean
    public FileSystemIngester fsIngester(IngestProperties ingestProperties, DataHandler dataHandler,
                                         IngestRecordRepository ingestRecordRepository, RedissonClient redissonClient,
                                         RuleManager ruleManager, AssetIngestBucketRecordService bucketRecordService) {
        log.trace("create new FileSystemIngester");
        return new FileSystemIngester(ingestProperties, dataHandler, ingestRecordRepository, redissonClient, ruleManager, bucketRecordService);
    }

    @Bean
    public IngesterEndpoint ingesterEndpoint(ApplicationContext context) {
        return new IngesterEndpoint(context);
    }

    @Bean
    public AssetIngestBucketRecordService bucketRecordService(BucketRecordRepository bucketRecordRepository) {
        return new AssetIngestBucketRecordService(bucketRecordRepository);
    }
}