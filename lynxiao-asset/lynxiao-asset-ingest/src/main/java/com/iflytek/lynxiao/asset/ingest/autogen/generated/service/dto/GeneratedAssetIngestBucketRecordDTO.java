package com.iflytek.lynxiao.asset.ingest.autogen.generated.service.dto;

import lombok.*;
import skynet.boot.mongo.domain.AbstractAuditingEntity;
import java.time.Instant;
import io.swagger.v3.oas.annotations.media.Schema;

@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class GeneratedAssetIngestBucketRecordDTO extends AbstractAuditingEntity<Long> {

    /**
     * 数据源id
     */
    @Schema(title = "数据源id")
    private Long datasourceId;

    /**
     * 桶编码
     */
    @Schema(title = "桶编码")
    private String bucketCode;

    /**
     * 数据路径
     */
    @Schema(title = "数据路径")
    private String path;

    /**
     * 符合规则数
     */
    @Schema(title = "符合规则数")
    private Long matchCount;

    /**
     * 错误数
     */
    @Schema(title = "错误数")
    private Long errorCount;

    /**
     * 状态
     */
    @Schema(title = "状态")
    private Integer status;

    /**
     * 开始时间
     */
    @Schema(title = "开始时间")
    private Instant startTime;

    /**
     * 结束时间
     */
    @Schema(title = "结束时间")
    private Instant endTime;

    /**
     * 删除标记
     */
    @Schema(title = "删除标记")
    private Boolean deleted;

    /**
     * 规则md5
     */
    @Schema(title = "规则md5")
    private String ruleMd5;

    /**
     * 已处理文件数
     */
    @Schema(title = "已处理文件数")
    private Integer processedFiles;

    /**
     * 总文件数
     */
    @Schema(title = "总文件数")
    private Integer totalFiles;
}