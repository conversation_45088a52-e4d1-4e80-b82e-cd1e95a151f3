package com.iflytek.lynxiao.asset.ingest.core;

import cn.hutool.core.thread.NamedThreadFactory;
import com.google.common.base.Preconditions;
import com.iflytek.lynxiao.asset.ingest.config.IngestProperties;
import com.iflytek.lynxiao.asset.ingest.dto.RuleDetail;
import com.iflytek.lynxiao.common.cache.AssetPortalCacheService;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.data.dto.asset.AssetRule;
import com.iflytek.lynxiao.data.dto.asset.SourceDTO;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import skynet.boot.pandora.api.ApiResponseGenerics;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 规则管理器类
 * 负责管理和维护数据摄取规则，定期从远程服务获取规则配置并更新本地规则缓存。
 * 该类使用定时任务定期扫描规则变更，并通过Feign客户端与远程服务进行通信。
 *
 * @author: leitong
 */
@Slf4j
public class RuleManager {

    /**
     * Spring应用上下文，用于获取和管理Bean
     */
    private final ApplicationContext applicationContext;

    /**
     * 数据摄取配置属性
     */
    private final IngestProperties properties;

    /**
     * 门户缓存
     */
    private final AssetPortalCacheService assetPortalCacheService;

    /**
     * 定时任务执行器，用于定期扫描规则变更
     */
    private final ScheduledExecutorService pool = Executors.newSingleThreadScheduledExecutor(new NamedThreadFactory("rule-manager-pool", false));

    /**
     * 规则缓存
     * Key: 数据源id
     * Value: 该存储桶对应的规则列表
     */
    private final Map<String, List<AssetRule>> sourceId2Rules = new ConcurrentHashMap<>();

    /**
     * 构造函数
     *
     * @param applicationContext Spring应用上下文
     * @param properties         数据摄取配置属性
     */
    public RuleManager(ApplicationContext applicationContext, IngestProperties properties, AssetPortalCacheService cacheService) {
        this.assetPortalCacheService = cacheService;
        this.applicationContext = applicationContext;
        this.properties = properties;
    }

    /**
     * 初始化方法，在Bean创建后自动执行
     * 启动定时任务，定期扫描规则变更
     */
    @PostConstruct
    public void start() {
        pool.scheduleWithFixedDelay(() -> {
            try {
                scanRules();
            } catch (Exception e) {
                log.error("fail to scan rules", e);
            }
        }, 10, properties.getRuleScanPeriod().toSeconds(), TimeUnit.SECONDS);
    }

    /**
     * 通过数据源ID获取关联的规则列表
     *
     * @param sourceId
     * @return
     */
    public List<AssetRule> getRules(String sourceId) {
        return Optional.ofNullable(this.sourceId2Rules.get(sourceId)).orElse(Collections.emptyList());
    }


    /**
     * 扫描规则变更
     * 从远程服务获取最新的规则配置，并更新本地规则缓存
     * 同时通知相关的IngesterManager刷新规则
     */
    private void scanRules() {
        // 获取数据源列表
        List<SourceDTO> assetSourceList = assetPortalCacheService.getAssetSource();
        List<SourceDTO> enabledSourceList = assetSourceList.stream().filter(SourceDTO::isEnabled).toList();
        log.debug("AssetSourceFeign response payload : {}", enabledSourceList);
        // 收集所有规则详情
        Map<String, SourceDTO> sourceDTOMap = new HashMap<>();
        for (SourceDTO source : enabledSourceList) {
            // 获取每个数据源的规则
            List<AssetRule> ruleList = assetPortalCacheService.getAssetRule(source.getId());
            log.debug("AssetRuleFeign response payload for source[{}]: {}", source.getId(), ruleList);
            if (Objects.nonNull(ruleList)) {
                ruleList.forEach(rule -> {
                    Preconditions.checkArgument(StringUtils.isNotBlank(rule.getAssetSourceId()));
                    if (rule.getAssetSourceId().equals(source.getId())) {
                        sourceDTOMap.putIfAbsent(rule.getAssetSourceId(), source);
                    }
                });
                // 更新规则缓存
                this.sourceId2Rules.put(source.getId(), ruleList);
            }
        }

        if (sourceDTOMap.isEmpty()) {
            log.debug("No data source found");
            return;
        }

        // 通知所有IngesterManager刷新规则
        Map<String, IngesterManager> beans = this.applicationContext.getBeansOfType(IngesterManager.class);
        Preconditions.checkArgument(!beans.isEmpty(), "fail to find IngesterManager beans");
        beans.values().forEach(ingesterManager -> ingesterManager.refresh(new ArrayList<>(sourceDTOMap.values())));
    }

    /**
     * 从API响应中获取有效载荷
     *
     * @param response API响应对象
     * @return 响应中的有效载荷
     * @throws LynxiaoException 当API调用失败时抛出
     */
    private <T> T getPayload(ApiResponseGenerics<T> response) {
        if (response.isSuccess()) {
            return response.getPayload();
        }
        throw new LynxiaoException("Feign request fail, response: " + response.toJson());
    }

    /**
     * 从规则详情列表中解析出唯一的数据源列表
     *
     * @param rules 规则详情列表
     * @return 去重后的数据源列表
     */
    private List<SourceDTO> parseSources(List<RuleDetail> rules) {
        Map<String, SourceDTO> map = new HashMap<>();
        for (RuleDetail ruleDetail : rules) {
            map.putIfAbsent(ruleDetail.getAssetSourceId(), ruleDetail.getSource());
        }
        return new ArrayList<>(map.values());
    }
}