package com.iflytek.lynxiao.asset.ingest.autogen.generated.domain;

import skynet.boot.mysql.domain.AbstractAuditingEntity;
import skynet.boot.common.utils.IdUtil;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.Instant;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.PrePersist;

/**
 * 数据接入记录
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "asset_ingest_bucket_record")
public class GeneratedAssetIngestBucketRecord extends AbstractAuditingEntity<Long> {
    /**
     * 数据源id
     */
    @Column(name = "datasource_id")
    private Long datasourceId;
    /**
     * 桶编码
     */
    @Column(name = "bucket_code")
    private String bucketCode;
    /**
     * 数据路径
     */
    @Column(name = "path")
    private String path;
    /**
     * 符合规则数
     */
    @Column(name = "match_count")
    private Long matchCount;
    /**
     * 错误数
     */
    @Column(name = "error_count")
    private Long errorCount;
    /**
     * 状态
     */
    @Column(name = "status")
    private Integer status;
    /**
     * 开始时间
     */
    @Column(name = "start_time")
    private Instant startTime;
    /**
     * 结束时间
     */
    @Column(name = "end_time")
    private Instant endTime;
    /**
     * 删除标记
     */
    @Column(name = "deleted")
    private Boolean deleted;
    /**
     * 规则md5
     */
    @Column(name = "rule_md5")
    private String ruleMd5;
    /**
     * 已处理文件数
     */
    @Column(name = "processed_files")
    private Integer processedFiles;
    /**
     * 总文件数
     */
    @Column(name = "total_files")
    private Integer totalFiles;

    @PrePersist
    public void prePersist() {
        if (this.getId() == null) {
            this.setId(IdUtil.getSnowflakeNextId());
        } 
    }


}