package com.iflytek.lynxiao.asset.ingest.core;

import cn.hutool.core.thread.NamedThreadFactory;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.asset.ingest.autogen.generated.domain.GeneratedAssetIngestBucketRecord;
import com.iflytek.lynxiao.asset.ingest.autogen.generated.domain.GeneratedAssetIngestRecord;
import com.iflytek.lynxiao.asset.ingest.config.IngestProperties;
import com.iflytek.lynxiao.asset.ingest.dto.BucketProcessStats;
import com.iflytek.lynxiao.asset.ingest.repo.IngestRecordRepository;
import com.iflytek.lynxiao.data.dto.asset.AssetRule;
import com.iflytek.lynxiao.data.task.TaskStatus;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.LineIterator;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 文件系统摄取器类，负责从文件系统中摄取数据。
 * 该类实现了Ingester接口，用于从文件系统数据源中摄取数据。
 * <p>
 * 主要功能：
 * 1. 定期扫描指定目录下的文件变化
 * 2. 处理新增的文件，确保文件只被处理一次
 * 3. 使用_DONE文件记录已处理的文件
 * 4. 将文件内容发送给DataHandler进行进一步处理
 *
 * <AUTHOR>
 * @date 2025/5/28 20:03
 */
@Slf4j
public class FileSystemIngester extends Ingester.Base {

    /**
     * 标记文件已处理的文件名
     * 用于记录目录下已处理过的文件列表
     */
    public static final String DONE_FILE = "_DONE";

    /**
     * 定时任务执行器
     * 用于定期扫描文件系统变化
     */
    private final ScheduledExecutorService executor = Executors.newSingleThreadScheduledExecutor(new NamedThreadFactory("file-ingest", false));

    /**
     * 摄取配置属性
     * 包含扫描周期等配置参数
     */
    private final IngestProperties ingestProperties;

    /**
     * 数据处理器
     * 用于处理从文件中读取的数据
     */
    private final DataHandler dataHandler;

    /**
     * 处理记录仓库
     * 用于保存数据摄取处理记录
     */
    private final IngestRecordRepository ingestRecordRepository;

    /**
     * Redisson客户端
     * 用于分布式锁，防止多节点重复处理同一目录
     */
    private final RedissonClient redissonClient;

    /**
     * 规则管理器
     * 用于获取数据源对应的规则
     */
    private final RuleManager ruleManager;

    /**
     * 桶记录服务
     * 用于管理桶处理记录
     */
    private final AssetIngestBucketRecordService bucketRecordService;

    /**
     * 监控的文件系统路径
     */
    private String path;

    /**
     * 关闭标志，用于优雅停止正在运行的任务
     */
    private volatile boolean closed = false;

    /**
     * 文件处理记录类
     * 用于记录单个文件的处理结果
     */
    @Getter
    @Setter
    @NoArgsConstructor
    private static class FileProcessRecord {
        private String fileName;
        private long totalLines;
        private long successLines;
        private long processTime;

        public FileProcessRecord(String fileName, long totalLines, long successLines) {
            this.fileName = fileName;
            this.totalLines = totalLines;
            this.successLines = successLines;
            this.processTime = System.currentTimeMillis();
        }

    }

    /**
     * 构造函数
     *
     * @param ingestProperties       摄取配置属性
     * @param dataHandler            数据处理器
     * @param ingestRecordRepository 处理记录仓库
     * @param redissonClient         Redisson客户端，用于分布式锁
     * @param ruleManager            规则管理器
     * @param bucketRecordService    桶记录服务
     */
    public FileSystemIngester(IngestProperties ingestProperties, DataHandler dataHandler, IngestRecordRepository ingestRecordRepository,
                              RedissonClient redissonClient, RuleManager ruleManager, AssetIngestBucketRecordService bucketRecordService) {
        this.ingestProperties = ingestProperties;
        this.dataHandler = dataHandler;
        this.ingestRecordRepository = ingestRecordRepository;
        this.redissonClient = redissonClient;
        this.ruleManager = ruleManager;
        this.bucketRecordService = bucketRecordService;
    }

    /**
     * 初始化摄取器
     *
     * @param sourceId 数据源ID
     * @param path     要监控的文件系统路径
     */
    public void initialize(String sourceId, String path) {
        this.sourceId = sourceId;
        this.path = path;
    }

    /**
     * 启动文件系统摄取过程
     * 该方法会启动一个定时任务，定期扫描文件系统变化
     * 首次扫描延迟15秒，之后按照配置的周期进行扫描
     */
    @Override
    public void start() {
        this.executor.scheduleAtFixedRate(this::scan, 15, ingestProperties.getRuleScanPeriod().getSeconds(), TimeUnit.SECONDS);
        log.info("FileSystemIngester started : {}", this.path);
    }

    /**
     * 停止文件系统摄取过程
     * 该方法会优雅地停止所有正在进行的扫描任务
     */
    @Override
    public void close() {
        log.info("开始关闭FileSystemIngester: sourceId={}, path={}", this.sourceId, this.path);

        // 设置关闭标志，让正在运行的任务能够检测到并优雅退出
        this.closed = true;
        this.executor.shutdownNow();

        // 清理Redis锁key，防止死锁
        cleanupRedisLocks();
    }

    /**
     * 清理Redis锁key
     */
    private void cleanupRedisLocks() {
        try {
            String lockKey = "ingest-file:source-lock:" + this.sourceId + this.path.replace(File.separator, ":");
            RLock sourceLock = redissonClient.getLock(lockKey);

            // 如果当前线程持有锁，则释放
            if (sourceLock.isHeldByCurrentThread()) {
                sourceLock.unlock();
                log.info("释放Redis锁: sourceId={}, path={}, lockKey={}", this.sourceId, this.path, lockKey);
            }

            // 强制删除锁key（谨慎使用，只在关闭时清理）
            if (sourceLock.isLocked()) {
                sourceLock.forceUnlock();
                log.warn("强制释放Redis锁: sourceId={}, path={}, lockKey={}", this.sourceId, this.path, lockKey);
            }
        } catch (Exception e) {
            log.error("清理Redis锁时发生异常: sourceId={}, path={}", this.sourceId, this.path, e);
        }
    }

    /**
     * 扫描文件系统变化
     * 比较当前文件快照与上次快照的差异，处理新增的文件
     */
    private void scan() {
        try {
            List<File> currentSnapshot = DirectoryDetector.scanReadyDirectory(path);
            logDirectories("scan current snapshot: {}", currentSnapshot);
            List<File> diff = new ArrayList<>(currentSnapshot.stream().filter(f -> !this.isFinishedDirectory(f)).toList());
            diff.sort(File::compareTo);
            logDirectories("scan diff: {}", diff);
            this.handleDiff(diff);
        } catch (Exception e) {
            log.error("error in scan", e);
        }
    }

    private void logDirectories(String text, List<File> directories) {
        if (log.isTraceEnabled()) {
            log.trace(text, JSON.toJSONString(directories.stream().map(File::getPath).toList()));
        }
    }

    /**
     * 基于新表检查目录是否已完成处理
     * 只有当该目录下所有相关的桶都已处理完成时，才认为目录已完成
     * 已完成包括：FINISHED（完成）和 CANCELED（取消）状态
     * 使用批量查询优化性能
     *
     * @param dir 目录
     * @return 是否已完成处理
     */
    private boolean isFinishedDirectory(File dir) {
        try {
            // 获取当前数据源的所有规则
            List<AssetRule> rules = ruleManager.getRules(this.sourceId);

            // 提取所有桶编码
            List<String> bucketCodes = rules.stream().map(AssetRule::getBucketCode).toList();

            // 批量检查所有桶是否都已处理完成（包括取消状态）
            return bucketRecordService.areAllBucketsProcessedOrCanceled(bucketCodes, dir.getAbsolutePath());
        } catch (Exception e) {
            log.error("检查目录处理状态时发生异常: {}", dir.getAbsolutePath(), e);
            return false; // 异常情况下认为未完成，允许重新处理
        }
    }

    /**
     * 处理文件差异
     * 对新增的文件进行处理
     * 使用全局path作为分布式锁，确保同一数据源的所有子目录作为整体处理
     *
     * @param diff 新增的文件列表
     */
    private void handleDiff(List<File> diff) {
        if (diff.isEmpty()) {
            return;
        }

        // 使用sourceId和path作为分布式锁的key，确保不同数据源不会冲突
        String lockKey = "ingest-file:source-lock:" + this.sourceId + this.path.replace(File.separator, ":");
        RLock sourceLock = redissonClient.getLock(lockKey);

        try {
            // 尝试获取数据源锁，等待30秒，使用watchdog自动续期
            boolean isLocked = sourceLock.tryLock(30, TimeUnit.SECONDS);
            if (!isLocked) {
                log.warn("数据源正在被其他节点处理中，跳过本次处理: sourceId={}, path={}, 待处理子目录数量: {}", this.sourceId, this.path, diff.size());
                return;
            }

            log.info("获取到数据源分布式锁（使用自动续期），开始批量处理子目录: sourceId={}, path={}, 子目录数量: {}", this.sourceId, this.path, diff.size());

            // 批量处理所有子目录 - 锁会通过watchdog自动续期
            processChildDirectoriesBatch(diff);

            log.info("完成数据源下所有子目录处理: sourceId={}, path={}, 处理数量: {}", this.sourceId, this.path, diff.size());

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("获取数据源锁时被中断: sourceId={}, path={}", this.sourceId, this.path, e);
        } catch (Exception e) {
            log.error("处理数据源时发生异常: sourceId={}, path={}", this.sourceId, this.path, e);
        } finally {
            if (sourceLock.isHeldByCurrentThread()) {
                sourceLock.unlock();
                log.debug("释放数据源分布式锁: sourceId={}, path={}", this.sourceId, this.path);
            }
        }
    }


    /**
     * 批量处理子目录列表
     *
     * @param childDirs 子目录列表
     */
    private void processChildDirectoriesBatch(List<File> childDirs) {
        int successCount = 0;
        int failCount = 0;

        for (File dir : childDirs) {
            // 在处理每个子目录前检查是否应该全局停止
            if (closed) {
                log.info("FileSystemIngester已停止，中止批量处理: sourceId={}, path={}, 已处理: {}/{}", this.sourceId, this.path, successCount + failCount, childDirs.size());
                break;
            }

            try {
                processDirectory(dir);
                successCount++;
                log.debug("成功处理子目录: {}", dir.getAbsolutePath());
            } catch (Exception e) {
                failCount++;
                log.error("处理子目录失败: {}", dir.getAbsolutePath(), e);
            }
        }

        log.info("批量处理子目录完成: 成功{}个, 失败{}个, 总计{}个", successCount, failCount, childDirs.size());
    }

    /**
     * 处理目录
     * 按照新方案：为每个桶分别处理目录，使用新表记录处理状态
     * 注意：此方法假设调用方已经获取了适当的分布式锁
     *
     * @param dir 要处理的目录
     * @throws IOException 文件操作异常
     */
    private void processDirectory(File dir) throws IOException {

        log.info("开始处理目录: {}", dir.getAbsolutePath());

        // 1: 在处理开始时，获取一次规则快照
        List<AssetRule> ruleSnapshot = ruleManager.getRules(this.sourceId);
        if (ruleSnapshot.isEmpty()) {
            log.warn("No rules found for sourceId '{}', skipping directory '{}'", this.sourceId, dir.getPath());
            return;
        }

        // 2: 过滤出需要处理的规则和初始化桶记录
        List<AssetRule> pendingRules = new ArrayList<>();
        Map<String, GeneratedAssetIngestBucketRecord> bucketRecords = new HashMap<>();

        // 3: 批量检查所有桶的处理状态
        List<String> allBucketCodes = ruleSnapshot.stream().map(AssetRule::getBucketCode).toList();
        Set<String> processedBuckets = bucketRecordService.getProcessedBuckets(allBucketCodes, dir.getAbsolutePath());
        Set<String> canceledBuckets = bucketRecordService.getCanceledBuckets(allBucketCodes, dir.getAbsolutePath());

        for (AssetRule rule : ruleSnapshot) {
            String bucketCode = rule.getBucketCode();

            // 4: 检查是否已处理或已取消
            if (processedBuckets.contains(bucketCode)) {
                log.info("Bucket [{}] has already processed directory [{}], skipping.", bucketCode, dir.getAbsolutePath());
                continue;
            }

            if (canceledBuckets.contains(bucketCode)) {
                log.info("Bucket [{}] has been canceled for directory [{}], skipping.", bucketCode, dir.getAbsolutePath());
                continue;
            }

            // 5: 开始处理，并记录到新表
            GeneratedAssetIngestBucketRecord bucketRecord = bucketRecordService.startOrGetProcessing(Long.valueOf(this.sourceId), bucketCode, dir.getAbsolutePath(), ruleSnapshot);

            pendingRules.add(rule);
            bucketRecords.put(bucketCode, bucketRecord);
        }

        if (pendingRules.isEmpty()) {
            log.info("所有桶都已处理完成，跳过目录: {}", dir.getAbsolutePath());
            return;
        }

        // 6: 一次性处理所有待处理的桶
        Map<String, Long> bucketMatchCounts = processDirectoryForSnapshotBuckets(dir, pendingRules, bucketRecords);

        // 7: 批量更新所有桶的处理结果
        for (AssetRule rule : pendingRules) {
            String bucketCode = rule.getBucketCode();
            GeneratedAssetIngestBucketRecord bucketRecord = bucketRecords.get(bucketCode);
            Long matchCount = bucketMatchCounts.getOrDefault(bucketCode, 0L);

            try {
                // 检查是否被取消
                if (bucketRecordService.isCanceled(bucketRecord.getId())) {
                    log.info("处理记录已被取消，保持取消状态: recordId={}, bucketCode={}", bucketRecord.getId(), bucketCode);
                    // 不需要额外操作，保持CANCELED状态
                } else if (closed) {
                    // 如果是因为全局停止（如close），标记为取消
                    bucketRecordService.cancelProcessing(bucketRecord.getId());
                    log.info("因全局停止信号取消处理记录: recordId={}, bucketCode={}", bucketRecord.getId(), bucketCode);
                } else {
                    // 正常完成
                    bucketRecordService.completeProcessing(bucketRecord.getId(), matchCount);
                }
            } catch (Exception e) {
                log.error("Failed to complete processing for bucket [{}] in directory [{}]", bucketCode, dir.getAbsolutePath(), e);
                // 只有在非取消状态下才标记为失败
                if (!bucketRecordService.isCanceled(bucketRecord.getId())) {
                    bucketRecordService.failProcessing(bucketRecord.getId());
                }
            }
        }

    }

    /**
     * 为所有待处理的桶一次性处理目录
     * 优化版本：一次扫描文件，对每行数据应用所有规则
     * 避免重复读取文件，提高处理效率
     *
     * @param dir          要处理的目录
     * @param pendingRules 待处理的规则列表
     * @return 每个桶的匹配数量 Map<bucketCode, matchCount>
     * @throws IOException 文件操作异常
     */
    private Map<String, Long> processDirectoryForSnapshotBuckets(File dir, List<AssetRule> pendingRules,
                                                                 Map<String, GeneratedAssetIngestBucketRecord> bucketRecords) throws IOException {
        Map<String, AtomicLong> bucketMatchCounts = new ConcurrentHashMap<>();

        // 初始化每个桶的计数器
        for (AssetRule rule : pendingRules) {
            bucketMatchCounts.put(rule.getBucketCode(), new AtomicLong(0));
        }

        File[] subFiles = dir.listFiles(f -> !isSkipFile(f)); // 获取所有非标记文件
        if (subFiles == null) {
            return bucketMatchCounts.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().get()));
        }

        log.info("开始处理目录 [{}]，待处理桶数量: {}，文件数量: {}", dir.getPath(), pendingRules.size(), subFiles.length);

        // 批量设置所有桶记录的总文件数，减少数据库操作
        List<Long> recordIds = bucketRecords.values().stream().map(GeneratedAssetIngestBucketRecord::getId).collect(Collectors.toList());
        bucketRecordService.batchSetTotalFiles(recordIds, subFiles.length);

        // 同时更新内存中的记录，保持一致性
        bucketRecords.values().forEach(record -> record.setTotalFiles(subFiles.length));

        // 【优化核心】: 一次扫描所有文件，对每行数据应用所有规则
        long totalLines = 0L;
        long successLines = 0L;
        int processedFileCount = 0;

        for (File subFile : subFiles) {
            // 在处理每个文件前检查是否应该全局停止
            if (closed) {
                log.info("FileSystemIngester已停止，中止文件处理: sourceId={}, path={}, 目录={}, 已处理文件: {}/{}", this.sourceId, this.path, dir.getAbsolutePath(), processedFileCount, subFiles.length);
                break;
            }

            // 检查并移除已取消的桶规则
            pendingRules = filterCanceledRules(pendingRules, bucketRecords);
            if (pendingRules.isEmpty()) {
                log.info("所有桶规则都已被取消，停止处理文件: sourceId={}, path={}, 目录={}", this.sourceId, this.path, dir.getAbsolutePath());
                break;
            }

            if (isSkipFile(subFile)) {
                continue;
            }

            ProcessResult result = processFile(subFile, pendingRules, bucketRecords);
            processedFileCount++; // 在这里增加已处理文件计数

            // 累加总行数和成功行数
            totalLines += result.totalLines();
            successLines += result.successLines();

            // 将文件级别的桶匹配数量累加到目录级别
            result.bucketMatchCounts().forEach((bucketCode, count) -> {
                bucketMatchCounts.computeIfAbsent(bucketCode, k -> new AtomicLong(0)).addAndGet(count.getMatchCount().get());
            });

            // 每个文件处理完后，更新对应记录的 match_count 和已处理文件数
            updateBucketRecordsProgress(result.bucketMatchCounts(), processedFileCount, bucketRecords);
        }

        // 这代表这个目录被扫描和处理过了，避免 FileSystemIngester 重复扫描
        // 使用累加的实际数据量和成功数量
        saveIngestRecord(dir, subFiles.length, totalLines, successLines);

        // 转换结果并记录日志
        Map<String, Long> result = bucketMatchCounts.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().get()));

        log.info("完成处理目录 [{}]，文件数量: {}，总行数: {}，成功行数: {}，各桶匹配数量: {}", dir.getPath(), subFiles.length, totalLines, successLines, result);
        return result;
    }

    /**
     * 处理单个文件
     * 按行读取文件内容并发送给数据处理器
     *
     * @param file          要处理的文件
     * @param pendingRules  待处理的规则列表
     * @param bucketRecords 桶记录映射
     * @return 处理结果，包含总行数、成功处理的行数和每个桶的匹配数量
     * @throws IOException 文件读取异常
     */
    private ProcessResult processFile(File file, List<AssetRule> pendingRules,
                                      Map<String, GeneratedAssetIngestBucketRecord> bucketRecords) throws IOException {
        log.info("Begin to process file : {}", file.getPath());

        Object lock = new Object();
        final AtomicLong totalLines = new AtomicLong(0L);
        final AtomicLong doneLines = new AtomicLong(0L);
        final AtomicLong successLines = new AtomicLong(0L);
        // 统计每个bucket的匹配成功、失败、入库成功数
        final Map<String, BucketProcessStats> bucketStatsMap = new ConcurrentHashMap<>();
        // 初始化bucketStatsMap
        for (AssetRule rule : pendingRules) {
            bucketStatsMap.put(rule.getBucketCode(), new BucketProcessStats());
        }

        // 保存原始规则列表大小，用于检测取消
        final int originalRulesSize = pendingRules.size();
        // 用于存储最新的规则列表
        final AtomicReference<List<AssetRule>> currentRules = new AtomicReference<>(pendingRules);

        Consumer<Exception> callback = e -> {
            doneLines.addAndGet(1);
            if (e == null) {
                successLines.addAndGet(1);
            }
            synchronized (lock) {
                lock.notifyAll();
            }
        };

        try (LineIterator it = FileUtils.lineIterator(file, "UTF-8")) {
            long lineCount = 0;
            while (it.hasNext()) {
                // 每处理10000行检查一次是否应该全局停止或规则被取消
                if (++lineCount % 10000 == 0) {
                    // 检查全局停止信号
                    if (closed) {
                        log.info("FileSystemIngester已停止，中止文件处理: {}, 已处理行数: {}", file.getPath(), lineCount);
                        break;
                    }
                    // 检查规则是否被取消
                    List<AssetRule> updatedRules = filterCanceledRules(currentRules.get(), bucketRecords);
                    currentRules.set(updatedRules);
                    // 如果所有规则都被取消，提前结束处理
                    if (updatedRules.isEmpty()) {
                        log.info("所有桶规则都已被取消，提前中止文件处理: {}, 已处理行数: {}", file.getPath(), lineCount);
                        break;
                    }
                    // 如果规则数量减少，记录日志
                    if (updatedRules.size() < originalRulesSize) {
                        log.info("部分桶规则已被取消，继续处理文件: {}, 原规则数: {}, 当前规则数: {}, 已处理行数: {}", file.getPath(), originalRulesSize, updatedRules.size(), lineCount);
                    }
                }

                String line = it.next().trim();
                if (StringUtils.isNotBlank(line)) {
                    totalLines.getAndAdd(1);
                    try {
                        log.trace("process line in file[{}] : {}", file.getPath(), line);
                        // 使用最新的规则列表处理数据
                        dataHandler.handleWithRulesAndStats(line, callback, currentRules.get(), bucketStatsMap);
                    } catch (Exception e) {
                        log.error("Failed to process line: {}", line, e);
                    }
                }
            }
        }
        synchronized (lock) {
            while (doneLines.get() < totalLines.get()) {
                try {
                    lock.wait();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Thread interrupted during wait", e);
                }
            }
        }

        log.info("File process finished: path[{}], total[{}], success[{}], bucketStats[{}]", file.getPath(), totalLines, successLines, bucketStatsMap);
        return new ProcessResult(totalLines.get(), successLines.get(), bucketStatsMap);
    }

    /**
     * 保存摄取记录
     *
     * @param dir          处理的目录
     * @param fileCount    处理的文件数量
     * @param totalLines   总行数
     * @param successLines 成功处理的行数
     */
    private void saveIngestRecord(File dir, int fileCount, long totalLines, long successLines) {
        try {
            GeneratedAssetIngestRecord record = GeneratedAssetIngestRecord.builder().datasourceId(Long.valueOf(sourceId))
                    .path(dir.getAbsolutePath()).filecount(fileCount).totalLines(totalLines).successLines(successLines)
                    .status(TaskStatus.FINISHED.getCode()).build();
            ingestRecordRepository.save(record);
        } catch (Exception e) {
            log.error("Failed to save ingest record for directory: {}", dir.getAbsolutePath(), e);
        }
    }

    /**
     * 读取已处理文件记录
     * 从_DONE文件中读取已处理过的文件记录
     *
     * @param dir 目录
     * @return 已处理文件记录列表
     * @throws IOException 文件读取异常
     */
    private List<FileProcessRecord> readDoneFiles(File dir) throws IOException {
        File file = new File(dir.getAbsolutePath() + File.separator + DONE_FILE);
        if (!file.exists() || !file.isFile()) {
            return Collections.emptyList();
        }
        List<FileProcessRecord> records = new ArrayList<>();
        List<String> lines = FileUtils.readLines(file, StandardCharsets.UTF_8);
        for (String line : lines) {
            if (StringUtils.isNotBlank(line.trim())) {
                try {
                    FileProcessRecord record = JSONObject.parseObject(line.trim(), FileProcessRecord.class);
                    records.add(record);
                } catch (Exception e) {
                    log.error("Failed to parse _DONE file record: {}", line, e);
                }
            }
        }
        return records;
    }

    /**
     * 比较两个文件快照的差异
     * 返回在after中存在但在before中不存在的文件
     *
     * @param before 之前的文件快照
     * @param after  当前的文件快照
     * @return 新增的文件列表
     */
    private static List<File> compare(List<File> before, List<File> after) {
        List<File> ret = new ArrayList<>();
        Map<String, File> beforeMap = before.stream().collect(Collectors.toMap(File::getPath, f -> f));
        for (File file : after) {
            if (!beforeMap.containsKey(file.getPath())) {
                ret.add(file);
            }
        }
        return ret;
    }

    /**
     * 是否是需要跳过的文件
     *
     * @param file
     * @return
     */
    private static boolean isSkipFile(File file) {
        return DirectoryDetector.READY_FLAG_FILE.equals(file.getName()) || DONE_FILE.equals(file.getName()) || file.getName()
                .startsWith(".");
    }


    /**
     * 过滤已取消的桶规则
     *
     * @param rules         原始规则列表
     * @param bucketRecords 桶记录映射
     * @return 过滤后的规则列表
     */
    private List<AssetRule> filterCanceledRules(List<AssetRule> rules, Map<String, GeneratedAssetIngestBucketRecord> bucketRecords) {
        List<AssetRule> filteredRules = new ArrayList<>();

        for (AssetRule rule : rules) {
            String bucketCode = rule.getBucketCode();
            GeneratedAssetIngestBucketRecord record = bucketRecords.get(bucketCode);

            if (record != null) {
                try {
                    if (bucketRecordService.isCanceled(record.getId())) {
                        log.info("桶规则已被取消，从处理列表中移除: bucketCode={}, recordId={}", bucketCode, record.getId());
                        continue; // 跳过这个规则
                    }
                } catch (Exception e) {
                    log.warn("检查桶规则取消状态时发生异常: bucketCode={}, recordId={}", bucketCode, record.getId(), e);
                }
            }

            filteredRules.add(rule);
        }

        return filteredRules;
    }

    /**
     * 文件处理结果类
     * 用于记录文件处理的统计信息
     */
    private record ProcessResult(long totalLines, long successLines, Map<String, BucketProcessStats> bucketMatchCounts) {
    }

    /**
     * 更新桶记录的进度信息（匹配数量和已处理文件数）
     * 每个文件处理完后，将该文件的匹配数量累加到对应的桶记录中，并更新已处理文件数
     *
     * @param fileBucketMatchCounts 文件级别的桶匹配数量
     * @param processedFileCount    已处理文件数
     * @param bucketRecords         桶记录映射
     */
    private void updateBucketRecordsProgress(Map<String, BucketProcessStats> fileBucketMatchCounts, int processedFileCount,
                                             Map<String, GeneratedAssetIngestBucketRecord> bucketRecords) {
        bucketRecords.forEach((bucketCode, record) -> {
            try {
                // 获取当前记录的匹配数量，累加新的匹配数量
                long currentMatchCount = record.getMatchCount() != null ? record.getMatchCount() : 0L;
                long currentErrorCount = record.getErrorCount() != null ? record.getErrorCount() : 0L;
                BucketProcessStats bucketProcessStats = fileBucketMatchCounts.get(bucketCode);
                long newMatchCount = currentMatchCount + (bucketProcessStats != null ? bucketProcessStats.getMatchCount().get() : 0L);
                long newErrorCount = currentErrorCount + (bucketProcessStats != null ? bucketProcessStats.getErrorCount().get() : 0L);

                // 一次性更新匹配数量和已处理文件数，减少数据库操作
                bucketRecordService.updateMatchCountAndProcessedFiles(record.getId(), newMatchCount, newErrorCount, processedFileCount);

                // 同时更新内存中的记录，保持一致性
                record.setMatchCount(newMatchCount);
                record.setProcessedFiles(processedFileCount);
                record.setErrorCount(newErrorCount);

                log.debug("更新桶记录进度: bucketCode={}, recordId={}, matchCount={}, errorCount={}, processedFiles={}", bucketCode, record.getId(), newMatchCount, newErrorCount, processedFileCount);
            } catch (Exception e) {
                log.error("更新桶记录进度失败: bucketCode={}, recordId={}, processedFiles={}", bucketCode, record.getId(), processedFileCount, e);
            }
        });
    }
}
