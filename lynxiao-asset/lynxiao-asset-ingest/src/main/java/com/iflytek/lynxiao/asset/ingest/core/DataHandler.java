package com.iflytek.lynxiao.asset.ingest.core;

import cn.hutool.core.thread.BlockPolicy;
import cn.hutool.core.thread.NamedThreadFactory;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.asset.ingest.config.IngestProperties;
import com.iflytek.lynxiao.asset.ingest.dto.BucketProcessStats;
import com.iflytek.lynxiao.common.datashard.DatashardEncoder;
import com.iflytek.lynxiao.common.snowflake.SnowflakeIdGenerator;
import com.iflytek.lynxiao.common.utils.AssetCellIdUtil;
import com.iflytek.lynxiao.common.utils.ExprUtil;
import com.iflytek.lynxiao.data.domain.*;
import com.iflytek.lynxiao.data.dto.asset.AssetBucketDTO;
import com.iflytek.lynxiao.data.dto.asset.AssetBucketStorageDTO;
import com.iflytek.lynxiao.data.dto.asset.AssetRule;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * 数据处理器类，负责处理从数据源摄取的数据。
 * 该类提供了一个线程池来异步处理数据，并定义了处理数据和错误的基本接口。
 * 具体的处理逻辑需要由子类实现。
 * <p>
 * 主要功能：
 * 1. 异步处理数据源摄取的数据
 * 2. 根据规则匹配数据
 * 3. 将匹配的数据进行编码和存储
 * 4. 支持数据的插入、更新和删除操作
 * 5. 处理数据的审计状态和加密标记
 *
 * @author: leitong
 * @date: 2025/5/29 17:37
 */
@Slf4j
public class DataHandler {

    /**
     * 数据摄取配置属性
     * 包含线程池配置、队列大小等参数
     * 用于控制数据处理的并发度和性能参数
     */
    private final IngestProperties properties;

    /**
     * 数据处理的线程池
     * 用于异步处理从数据源摄取的数据
     * 支持可配置的核心线程数、最大线程数和队列大小
     */
    public final ExecutorService pool;

    /**
     * 规则管理器
     * 负责管理和获取数据源对应的处理规则
     * 用于确定数据应该如何处理和存储
     */
    private final RuleManager ruleManager;

    /**
     * 存储桶服务
     * 负责将处理后的数据保存到对应的存储桶中
     * 管理数据的持久化存储
     */
    private final BucketStorageService bucketStorageService;

    /**
     * 数据分片编码器
     * 用于对文档大字段进行存储分离
     * 优化数据存储结构和查询性能
     */
    private final DatashardEncoder datashardEncoder;

    private final MeterRegistry meterRegistry;

    private final SnowflakeIdGenerator idGenerator;

    /**
     * 构造函数
     * 初始化数据处理器，创建线程池
     *
     * @param properties           数据摄取配置属性，包含线程池配置等参数
     * @param ruleManager          规则管理器，用于获取数据源对应的处理规则
     * @param snowflakeIdGenerator 雪花算法
     */
    public DataHandler(IngestProperties properties, RuleManager ruleManager, BucketStorageService bucketStorageService,
                       DatashardEncoder datashardEncoder, MeterRegistry meterRegistry, SnowflakeIdGenerator snowflakeIdGenerator) {
        this.properties = properties;
        this.pool = new ThreadPoolExecutor(properties.getHandler().getCoreThreads(),    // 核心线程数
                properties.getHandler().getMaxThreads(),     // 最大线程数
                10,                                         // 空闲线程存活时间
                TimeUnit.MINUTES,                           // 时间单位
                new LinkedBlockingQueue<>(properties.getHandler().getQueueSize()),  // 工作队列
                new NamedThreadFactory("data-handler-pool", false),  // 线程工厂
                new BlockPolicy()                           // 拒绝策略
        );
        this.ruleManager = ruleManager;
        this.bucketStorageService = bucketStorageService;
        this.datashardEncoder = datashardEncoder;
        this.meterRegistry = meterRegistry;
        this.idGenerator = snowflakeIdGenerator;
    }

    /**
     * 处理从数据源摄取的数据
     * 该方法将数据处理任务提交到线程池中异步执行
     * 处理流程：
     * 1. 解析输入数据为JSON对象
     * 2. 获取数据源对应的处理规则
     * 3. 对每条规则进行匹配处理
     * 4. 处理完成后通过回调函数通知结果
     *
     * @param dataSourceId 数据源ID，用于标识数据的来源
     * @param data         要处理的数据内容，JSON格式的字符串
     * @param callback     处理完成后的回调函数，用于通知处理结果
     */
    public void handle(String dataSourceId, String data, Consumer<Exception> callback) {
        List<AssetRule> rules = this.ruleManager.getRules(dataSourceId);
        handleWithRulesInternal(data, callback, rules, null);
    }

    /**
     * 带统计功能的数据处理方法
     * 该方法将数据处理任务提交到线程池中异步执行，并统计每个桶的匹配数量
     * 处理流程：
     * 1. 解析输入数据为JSON对象
     * 2. 对传入的规则列表进行匹配处理
     * 3. 统计每个桶的匹配数量
     * 4. 处理完成后通过回调函数通知结果
     *
     * @param data              要处理的数据内容，JSON格式的字符串
     * @param callback          处理完成后的回调函数，用于通知处理结果
     * @param rules             要应用的规则列表
     * @param bucketMatchCounts 用于统计每个桶匹配数量的Map
     */
    public void handleWithRulesAndStats(String data, Consumer<Exception> callback, List<AssetRule> rules,
                                        Map<String, BucketProcessStats> bucketMatchCounts) {
        handleWithRulesInternal(data, callback, rules, bucketMatchCounts);
    }

    /**
     * 内部通用的数据处理方法
     * 该方法将数据处理任务提交到线程池中异步执行
     * 处理流程：
     * 1. 解析输入数据为JSON对象
     * 2. 对传入的规则列表进行匹配处理
     * 3. 可选地统计每个桶的匹配数量
     * 4. 处理完成后通过回调函数通知结果
     *
     * @param data              要处理的数据内容，JSON格式的字符串
     * @param callback          处理完成后的回调函数，用于通知处理结果
     * @param rules             要应用的规则列表
     * @param bucketMatchCounts 用于统计每个桶匹配数量的Map，可为null
     */
    private void handleWithRulesInternal(String data, Consumer<Exception> callback, List<AssetRule> rules,
                                         Map<String, BucketProcessStats> bucketMatchCounts) {
        this.pool.submit(() -> {
            Exception ex = null;
            long start = System.nanoTime();
            boolean success = true;
            try {
                JSONObject dataJson = JSONObject.parseObject(data);
                for (AssetRule rule : rules) {
                    try {
                        boolean matched = handle(rule, dataJson);
                        if (bucketMatchCounts != null && matched) {
                            BucketProcessStats bucketProcessStats = bucketMatchCounts.get(rule.getBucketCode());
                            if (bucketProcessStats != null) {
                                bucketProcessStats.getMatchCount().incrementAndGet();
                            }
                        }
                    } catch (Exception e) {
                        log.error("handle rules error", e);
                        if (bucketMatchCounts != null) {
                            BucketProcessStats stats = bucketMatchCounts.get(rule.getBucketCode());
                            if (stats != null) {
                                stats.getErrorCount().incrementAndGet();
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("exception in handler", e);
                ex = e;
                success = false;
            } finally {
                long duration = System.nanoTime() - start;
                meterRegistry.counter("lynxiao.asset.ingest.data.handle.total", Tags.of("success", String.valueOf(success))).increment();
                meterRegistry.timer("lynxiao.asset.ingest.data.handle.time", Tags.of("success", String.valueOf(success)))
                        .record(duration, TimeUnit.NANOSECONDS);
                callback.accept(ex);
            }
        });
    }

    /**
     * 处理单条规则与数据的匹配
     * 如果规则列表为空，则默认匹配成功
     * 否则检查数据是否满足规则中的任一条件
     * 匹配成功后进行数据保存
     *
     * @param rule     处理规则，包含匹配条件和存储桶信息
     * @param dataJson 待处理的数据，JSON格式
     * @throws Exception 处理过程中可能出现的异常
     */
    private boolean handle(AssetRule rule, JSONObject dataJson) throws Exception {
        try {
            boolean match;
            if (CollectionUtils.isEmpty(rule.getConditions())) {
                match = true;
            } else {
                List<JSONObject> ruleJsonList = rule.getConditions().stream().map(AssetRule.Condition::getCondition).map(JSONObject::parse)
                        .toList();
                match = ruleJsonList.stream().anyMatch(ruleJson -> ExprUtil.isConditionMet(ruleJson, dataJson));
            }
            if (match) {
                this.save(rule, dataJson);
            }
            return match;
        } catch (Exception e) {
            log.error("data handler has ex. bucketCode:{}", rule.getBucketCode(), e);
            throw e;
        }
    }

//    /**
//     * 发送匹配成功的数据到Kafka消息队列
//     * 消息格式：
//     * {
//     *   "bucketCode": "规则对应的存储桶代码",
//     *   "data": "原始数据内容"
//     * }
//     *
//     * @param rule 匹配成功的规则
//     * @param dataJson 待发送的数据
//     */
//    private void send(RuleDTO rule, JSONObject dataJson) {
//        ApiRequest msg = new ApiRequest();
//        msg.setPayload(new JSONObject().fluentPut("bucketCode", rule.getBucketCode()).fluentPut("data", dataJson));
//        String key = UUID.randomUUID().toString();
//        this.kafkaTemplate.send(this.properties.getIngestTopicName(), key, msg.toJson());
//    }

    /**
     * 保存处理后的数据
     * 处理流程：
     * 1. 对数据进行编码处理
     * 2. 设置操作类型（插入/更新/删除）
     * 3. 添加时间戳和审计状态
     * 4. 设置加密和删除标记
     * 5. 保存到对应的存储桶
     *
     * @param rule     处理规则，包含存储桶信息
     * @param dataJson 待保存的数据
     * @throws Exception 保存过程中可能出现的异常
     */
    private void save(AssetRule rule, JSONObject dataJson) throws Exception {
        // 分离较长的字段
        AssetCell cell = AssetCell.from(datashardEncoder.encode(rule.getBucketCode(), dataJson));
        // 处理id
        AssetCellIdUtil.idProcess(cell, idGenerator);

        AssetBucketDTO dto = this.bucketStorageService.getBucketStorage(rule.getBucketCode());

        // 校验字段属性值
        cell.validateFieldSpec(dto.getFieldSpec());

        AssetCellProps props = AssetCellProps.createWithDefaults();
        // 解析操作枚举，如果原数据中含有_x.op，则覆盖到新的_x中
        AssetOperationType opInData = op(cell);
        if (Objects.nonNull(opInData)) {
            props.setOperationType(opInData);
        }
        // 确定审核状态
        AssetBucketStorageDTO bucketStorage = this.bucketStorageService.getBucketStorage(rule.getBucketCode());

        AssetAuditStatus auditStatus = AssetAuditStatus.of(bucketStorage.getAuditType());

        props.setAuditStatus(auditStatus, new AssetAuditor());
        // 设置保存的桶
        props.setSourceBucketCode(rule.getBucketCode());
        // 设置系统内置属性
        cell.put(AssetCellProps.PRESET_ATTRIBUTE, props);
        this.bucketStorageService.save(rule.getBucketCode(), cell);
    }


    /**
     * 从数据中解析已经存在的_x.op
     *
     * @param data
     * @return
     */
    private AssetOperationType op(Map<String, Object> data) {
        AssetCell cell = AssetCell.from(data);
        return Optional.ofNullable(cell.getProps()).map(AssetCellProps::getOperationType).orElse(null);
    }


}