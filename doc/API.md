# Lynxiao-产品接口协议

# 1. 业务接入流程

![](images/c83eabae0096778ffc36e4ff60322f0ed69ffcf785b63c9ef2ba1ff6963c8366.jpg)

# 2. 搜索接口协议(v1)

## 2.1 路由接口

请求地址：

【内网】：`POST: http://lynxiao-search-api-hf-internal.xf-yun.com/v1/route`

附：签名算法

请求示例：

```json
{
   "header": {
        "traceId": "206b0e7e676f",
        "appId": "00000000",
        "prodCode": "health",
        "envType": 2
    },
    "payload": {
       "idc":"sh"
    }
}
```



请求字段说明：

<table><tr><td>参数名</td><td>含义</td><td>类型</td><td>是否必传</td><td>默认值</td><td>备注</td></tr><tr><td>header</td><td>消息头</td><td>Object</td><td>是</td><td>-</td><td></td></tr><tr><td>• header.traceId</td><td>链路唯一标识</td><td>string</td><td>是</td><td>-</td><td></td></tr><tr><td>• header.appId</td><td>搜索平台授权的 AppId</td><td>string</td><td>是</td><td>-</td><td>与具体的搜索产品经理对接获得</td></tr><tr><td>• header.prodCode</td><td>搜索平台的产品编码</td><td>string</td><td>是</td><td>-</td><td>与具体的搜索产品经理对接获得</td></tr><tr><td>• header.envType</td><td>服务环境标签</td><td>int</td><td>否</td><td>2</td><td>生产环境必须设置为2
1：验证环境
2：生产环境</td></tr><tr><td>payload</td><td>消息体</td><td>Object</td><td>是</td><td>{</td><td></td></tr><tr><td>payload.idc</td><td>机房标识</td><td>string</td><td>否</td><td>-</td><td>有指定机房需求时可使用该字
段，与具体的搜索产品经理对接
需求
sh：上海机房（生产环境）
dx：森华机房（生产环境）
hf：合肥机房（验证环境）</td></tr></table>
返回示例：

```json
{
    "header": {
        "traceId": "206b0e7e676f",
        "code": 0,
        "message": "success"
    },
    "payload": {
        "regionCode": "sh",
        "regionUrl": "http://lynxiao-search-api-hu.xf-yun.com/v1/search",
        "regionInnerUrl": "http://lynxiao-search-api-hu-internal.xf-yun.com/v1/search",
        "regionOuterUrl": "http://lynxiao-search-api-hu.xf-yun.com/v1/search",
        "pvid": "1861252639883395072",
        "processId": "675a9611ce2b8601249c92f9",
        "token": "eyJhcHBJZCI6IjAwMCIsImRhdGUiOjE3MzQwNzA5MTA3ODMsInByb2RDb2RlIjoiSkJXVV8xIiwicHZpZCI6IjE4NjcxMTUxMDYyMjgzOTYwMzIiLCJyZWdpb24iOiJzaCIsInNpZ25hdHVyZSI6IjltdCtNT3RPT3kwa1U5M0Y0d3libENHVTRGUXFpYURHK2JQYWxmYkdVK0E9In0="
    }
}
```



响应字段说明：

<table><tr><td>参数名</td><td>含义</td><td>类型</td><td>是否必传</td><td>默认值</td><td>备注</td></tr><tr><td>header</td><td>响应消息头</td><td>Object</td><td>是</td><td>-</td><td></td></tr><tr><td>• header.traceld</td><td>链路唯一标识</td><td>string</td><td>是</td><td>-</td><td></td></tr><tr><td>• header.code</td><td>响应编码</td><td>int</td><td>是</td><td>0</td><td>非0是异常数据</td></tr><tr><td>• header.messag e</td><td>响应消息说明</td><td>string</td><td>否</td><td></td><td></td></tr><tr><td>payload</td><td>响应消息体</td><td>Object</td><td>是</td><td>-</td><td></td></tr><tr><td>• payload.region Code</td><td>区域编码</td><td>String</td><td></td><td></td><td>示例：sh</td></tr><tr><td>• payload.region Urt</td><td>区域请求searchAPI地址</td><td>string</td><td></td><td></td><td>过期参数，后续下线
http://lynxiao-search-api-hu.xf-yun.com/v1/search</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr></table>

<table><tr><td>·payload.regionl nnnerUrl</td><td>区域请求searchAPI 内网地址</td><td>string</td><td></td><td></td><td>http://lynxiao-search-api-hf-internal.xf-yun.com/v1/search</td></tr><tr><td>·payload.region OuterUrl</td><td>区域请求searchAPI 外网地址</td><td>string</td><td></td><td></td><td>http://lynxiao-search-api-hf.xf-yun.com/v1/search</td></tr><tr><td>·payload.pvid</td><td>产品方案版本ID</td><td>String</td><td></td><td></td><td>示例：
1861252639883395072</td></tr><tr><td>·payload.process sld</td><td>产品方案版本流程Id</td><td>String</td><td></td><td></td><td>示例：
675797478cde1539962db5f 4</td></tr><tr><td>·payload.token</td><td>搜索请求token</td><td>String</td><td></td><td></td><td>搜索服务校验，会有有效期（默认5分钟）</td></tr></table>
## 2.2 搜索接口

请求地址

【合肥内网】：`POST:http://lynxiao-search-api-hf-internal.xf-yun.com/v1/search`

【上海内网】：`POST:http://lynxiao-search-api-hu-internal.xf-yun.com/v1/search`

由路由服务返回regionInnerUrl（内网），regionOuterUrl（外网）

附：签名算法

搜索接口的入参和出参中的payload协议取决于具体的产品方案，调用前请和S研发部产品经理同事确认！

请求示例

```json
{
    "header": {
        "traceId": "61cb9e12b7a2",
        "appId":"00000000",            // 业务侧请联系产品经理提前获取     
        "prodCode": "HealthySearch",      // 业务侧请联系产品经理提前获取
        "token": "eyJhcHBJZCI6IjAwMCIsImRhdGUiOjE3MzQwNzA5MTA3ODMsInByb2RDb2RlIjoiSkJXVV8xIiwicHZpZCI6IjE4NjcxMTUxMDYyMjgzOTYwMzIiLCJyZWdpb24iOiJzaCIsInNpZ25hdHVyZSI6IjltdCtNT3RPT3kwa1U5M0Y0d3libENHVTRGUXFpYURHK2JQYWxmYkdVK0E9In0="
    },
    "parameter": {
        // 产品方案流程id
        "id": "675797478cde1539962db5f4"
    },
    "payload": {
        // 业务参数
    }
}
```



请求字段说明

<table><tr><td>参数名</td><td>含义</td><td>类型</td><td>是否必
传</td><td>默认值</td><td>备注</td></tr><tr><td>header</td><td>请求头</td><td>Object</td><td>是</td><td>-</td><td></td></tr><tr><td>• header.traceld</td><td>链路唯一标识</td><td>string</td><td>是</td><td>-</td><td></td></tr><tr><td>• header.appld</td><td>用户的appld信
息</td><td>string</td><td>是</td><td>-</td><td></td></tr><tr><td>• header.prodCod
e</td><td>搜索平台的产
品编码</td><td>string</td><td>是</td><td>-</td><td>与具体的搜索产品经理对接获
得
示例：HealthySearch</td></tr><tr><td>• header.token</td><td>路由服务返回
的token</td><td>string</td><td>是</td><td></td><td>一定需要传（会有时间有效
性）</td></tr><tr><td>• header.uid</td><td>用户级别
user_id</td><td>string</td><td>否</td><td>-</td><td>未强校验，建议长度0-25</td></tr><tr><td>• header.*</td><td>约定的其他属
性</td><td>String</td><td>否</td><td></td><td>根据具体的业务约定</td></tr><tr><td>parameter</td><td>此次回话参数
模块</td><td>Object</td><td>是</td><td>-</td><td></td></tr><tr><td>• parameter.id</td><td>产品方案流程Id</td><td>String</td><td>是</td><td>-</td><td>示例：
675797478cde1539962db5f4</td></tr><tr><td>payload</td><td>请求体</td><td>Object</td><td>是</td><td>-</td><td>具体参照不同业务协议</td></tr></table>


返回数据示例

```json
{
    "header": {
        "code": 0,
        "traceId": "b0316e5fc8b64c888b59fde37f54c432"
    },
    "payload": {
        "output": {
           // 业务参数
        }
    }
}
```



响应数据说明

<table><tr><td>参数名</td><td>含义</td><td>类型</td><td>备注</td><td>样例</td></tr><tr><td>header</td><td>头信息</td><td>Object</td><td>code</td><td></td></tr><tr><td>header.code</td><td>结果状态码</td><td>Int</td><td></td><td>&quot;code&quot;:0</td></tr><tr><td>header.message</td><td>对状态码的描述</td><td>String</td><td></td><td>&quot;message&quot;: &quot;success&quot;</td></tr><tr><td>header.traceld</td><td>链路ID，与请求保持一致</td><td>String</td><td>请求没有traceld，自动生成</td><td>&quot;traceld&quot;: &quot;**********&quot;</td></tr><tr><td>header.sid</td><td>客户端透传的sid</td><td>String</td><td></td><td>&quot;sid&quot;: &quot;search-flow-medical000100667@dx193b450c29db9e4401&quot;,</td></tr><tr><td>payload</td><td>返回载体</td><td>Object</td><td>code</td><td></td></tr></table>



### 2.2.1垂类搜索（Search)

- 入参

完整示例

```json
{
    "header": {
        "traceId": "61cb9e12b7a2",
        "appId":"00000000",
        "prodCode": "Search",//产品方案编码，具体与产品经理沟通获取
        "token": "eyJhcHBJZCI6IjAwMCIsImRhdGUiOjE3MzQwNzA5MTA3ODMsInByb2RDb2RlIjoiSkJXVV8xIiwicHZpZCI6IjE4NjcxMTUxMDYyMjgzOTYwMzIiLCJyZWdpb24iOiJzaCIsInNpZ25hdHVyZSI6IjltdCtNT3RPT3kwa1U5M0Y0d3libENHVTRGUXFpYURHK2JQYWxmYkdVK0E9In0="
    },
    "parameter": {
        // 产品方案流程id
        "id": "675797478cde1539962db5f4"
    },
    "payload": {
        // 搜索业务参数
        "query": [
            "牙周炎"
        ],
        "scene": ["诊疗-治疗方案-检查检验治疗方案","诊疗-治疗方案-其他"]  
        "intent":"5001", //搜索意图id，业务侧请联系产品经理提前获取  
        "filter":{
            "filedName":"post_ts", //检索字段
            "startTime":123456, //秒
            "endTime":123456, //秒
            "domain":["sina.com.cn"]
        },
    }
}
```



参数说明

<table><tr><td>参数名</td><td>含义</td><td>类型</td><td>是否必
传</td><td>默认值</td><td>备注</td></tr><tr><td>header</td><td>请求头</td><td>Object</td><td>是</td><td>-</td><td></td></tr><tr><td>• header.traceld</td><td>链路唯一标识</td><td>string</td><td>是</td><td>-</td><td></td></tr><tr><td>• header.appld</td><td>用户的appld信
息</td><td>string</td><td>是</td><td>-</td><td>一般不动</td></tr><tr><td>• header.prodCode</td><td>搜索平台的产
品编码</td><td>string</td><td>是</td><td>-</td><td>与具体的搜索产品经理对接获
得</td></tr><tr><td>• header.token</td><td></td><td>string</td><td>是</td><td></td><td></td></tr></table>

<table><tr><td></td><td>的token</td><td></td><td></td><td></td><td></td></tr><tr><td>·header.uid</td><td>用户级别 user_id</td><td>string</td><td>否</td><td>-</td><td>未强校验，建议长度0-25</td></tr><tr><td>·header.*</td><td>约定的其他属性</td><td>String</td><td>否</td><td></td><td>根据具体的业务约定</td></tr><tr><td>parameter</td><td>此次回话参数模块</td><td>Object</td><td>是</td><td>-</td><td></td></tr><tr><td>·parameter.id</td><td>产品方案流程Id</td><td>String</td><td>是</td><td>-</td><td>示例：
675797478cde1539962db5f 4</td></tr><tr><td>payload</td><td>请求体</td><td>Object</td><td>是</td><td>-</td><td></td></tr><tr><td>·payload.query</td><td>用户问题</td><td>[string</td><td>是</td><td></td><td>【产品方案|场景策略】请求问题0-255B
当前只处理单条query
二期支持高级DSL</td></tr><tr><td>·payload.appid</td><td>搜索平台授权的Appid</td><td>string</td><td>是</td><td>-</td><td>与具体的搜索产品经理对接获得</td></tr><tr><td>·payload.intent</td><td>搜索意图</td><td>string</td><td>否</td><td></td><td>按照id调用对应领域的策略，可联系产品经理获取
1、精品库策略
2、权威知识库
3、聚合搜索
4、医典知识库
5、guide检索</td></tr><tr><td>·payload.scene</td><td>医疗场景标签</td><td>[string</td><td>否</td><td></td><td>医疗传参
*场景分类
private List&lt;string&gt; sceneClassification;</td></tr><tr><td>·payload.plugin</td><td>插件分类</td><td>String</td><td>否</td><td>-</td><td>医疗参数
*插件分类
private String pluginClassification;</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr></table>

<table><tr><td>·payload.fromType</td><td>来源类型</td><td>string</td><td>否</td><td></td><td>【产品方案|场景策略】query 具体来源（以医疗实际传入为 准）
0：晓医_APP端
1：星火desk_PC端
2：星火desk_APP端
3：星火desk_小程序端
*数据来源
private String dataSource;</td></tr><tr><td>·payload.topK</td><td>最终返回结果数量</td><td>int</td><td>否</td><td></td><td>有需求可找产品运营配置策略</td></tr><tr><td>·payload.filter</td><td>业务自定义过滤条件</td><td>Object</td><td>否</td><td>-</td><td>【场景策略】</td></tr><tr><td>·payload.filter.filed Name</td><td>时间过滤字段</td><td>String</td><td>否</td><td></td><td>用来进行时间过滤的字段</td></tr><tr><td>·payload.filter.end Time</td><td>过滤结束时间</td><td>Long</td><td>否</td><td></td><td>搜索结果起始时间，单位s</td></tr><tr><td>·payload.filter.start Time</td><td>过滤起始时间</td><td>Long</td><td>否</td><td></td><td>搜索结果结束时间，单位s</td></tr><tr><td>·payload.filter.dom ain</td><td>过滤站点域名</td><td>】String</td><td>否</td><td></td><td>搜索结果站点域名</td></tr></table>


出参

```json
{
    "header": {
        "code": 0,
        "traceId": "b0316e5fc8b64c888b59fde37f54c432"
    },
    "payload": {
        "output": {
            "data": [
                {
                    "docs": [
                        {
                            "content": "糖类抗原是临床上比较常用的一种肿瘤标记物...ellipses 1684 char...了解治疗效果，能够及时调整后续治疗方案。",
                            "depart": "肿瘤科",
                            "doctor_avatar": "https://muzhi-public-pic.cdn.bcebos.com/100095221220",
                            "doctor_level": "主任医师",
                            "doctor_name": "赵赟博",
                            "gid": "d537c5c5e7d1789191ba630a4550f392c2e7fe075ba6ac47c24f496e9b9ca6ef",
                            "hospital": "北京医院",
                            "hospital_level": "三甲",
                            "id": 1264362453640407397,
                            "index": 1,
                            "indexCode": "IDXCXHSLW_DSxy9e2c_V008",
                            "levels": {
                                "L06": 4
                            },
                            "o_url": "https://www.liangyiyy.com/article/info/1506700.html",
                            "post_ts": 1704211200,
                            "site": "m.baidu.com",
                            "ss": [
                                {
                                    "id": 1264362453640407397,
                                    "span": [
                                        0,
                                        862
                                    ]
                                }
                            ],
                            "summary": "糖类抗原是临床上比较常用的一种肿瘤标记物...ellipses 1684 char...了解治疗效果，能够及时调整后续治疗方案。",
                            "title": "糖类抗原增高是怎么回事",
                            "url": "https://m.baidu.com/bh/m/detail/ar_17267368949280780400"
                        },
                        {
                            "content": "病情分析：病情诊断：\n糖类抗原（CA）高...ellipses 1148 char...生活习惯可有助于身体健康，预防疾病发生。",
                            "depart": "肿瘤科",
                            "doctor_avatar": "https://muzhi-public-pic.cdn.bcebos.com/100015647263",
                            "doctor_level": "副主任医师",
                            "doctor_name": "姚忠强",
                            "gid": "d93cca7ef6fca330c530dc8832b2f02c48498d44514981c0f8aeeb5d7b74f378",
                            "hospital": "通用医疗三二〇一医院",
                            "hospital_level": "三甲",
                            "id": 5486819298503450385,
                            "index": 2,
                            "indexCode": "IDXCXHSLW_DSxy9e2c_V008",
                            "levels": {
                                "L06": 4
                            },
                            "o_url": "http://muzhi.baidu.com/content_production/ai_quiz_420463362700544.html",
                            "post_ts": 0,
                            "site": "m.baidu.com",
                            "ss": [
                                {
                                    "id": 5486819298503450385,
                                    "span": [
                                        0,
                                        594
                                    ]
                                }
                            ],
                            "summary": "病情分析：病情诊断：\n糖类抗原（CA）高...ellipses 1148 char...生活习惯可有助于身体健康，预防疾病发生。",
                            "title": "糖类抗原高是什么意思-医疗科普-百度健康",
                            "url": "https://m.baidu.com/bh/m/detail/qr_8633207070972909212"
                        },
                        {
                            "content": "在就诊时，我接诊了一位45岁的患者，他说...ellipses 1036 char...及时就医检查，以科学的态度面对健康问题。",
                            "depart": "肿瘤科",
                            "doctor_level": "主任医师",
                            "doctor_name": "朱希山",
                            "gid": "a401c0d6d5296c4ea83834161620f64baedaab0bb15f788888bcd83e71518fcb",
                            "hospital": "北京大学第一医院",
                            "hospital_level": "三甲",
                            "id": 7987406734702536047,
                            "index": 3,
                            "indexCode": "IDXCXHSLW_DSxy9e2c_V008",
                            "levels": {
                                "L06": 4
                            },
                            "post_ts": 1709913600,
                            "site": "miaoshou.net",
                            "ss": [
                                {
                                    "id": 7987406734702536047,
                                    "span": [
                                        0,
                                        538
                                    ]
                                }
                            ],
                            "summary": "在就诊时，我接诊了一位45岁的患者，他说...ellipses 1036 char...及时就医检查，以科学的态度面对健康问题。",
                            "title": "糖类抗原偏高，意味着什么？",
                            "url": "https://www.miaoshou.net/article/PgN9Vn0BvDQ2OrWa.html"
                        },
                        {
                            "content": "糖类抗原高一般说明患者存在不良习惯、炎症...ellipses 614 char...可能是妊娠等原因，需要做相关检查和治疗。",
                            "depart": "肿瘤科",
                            "doctor_level": "副主任医师",
                            "doctor_name": "祁志荣 ",
                            "gid": "5432b22feed2682ab27d7be9c338745f7f06b45b613884ccb7df3c13ec0a4b33",
                            "hospital": "中日友好医院",
                            "hospital_level": "三甲",
                            "id": 4170399859818799684,
                            "index": 4,
                            "indexCode": "IDXCXHSLW_DSxy9e2c_V008",
                            "levels": {
                                "L06": 4
                            },
                            "post_ts": 1671292800,
                            "site": "cndzys.com",
                            "ss": [
                                {
                                    "id": 4170399859818799684,
                                    "span": [
                                        0,
                                        327
                                    ]
                                }
                            ],
                            "summary": "糖类抗原高一般说明患者存在不良习惯、炎症...ellipses 614 char...可能是妊娠等原因，需要做相关检查和治疗。",
                            "title": "糖类抗原高说明什么",
                            "url": "https://www.cndzys.com/ylcore/art_detail/1_1092384.html"
                        },
                        {
                            "content": "糖类抗原高有可能是正常反应、妊娠、肿瘤疾...ellipses 566 char...是药物因素等原因，需要做相关检查和治疗。",
                            "depart": "肿瘤科",
                            "doctor_level": "副主任医师",
                            "doctor_name": "祁志荣 ",
                            "gid": "af5d2f540d5798bbe974be2f2159cc369daf909c21a99de87db1648d18e67705",
                            "hospital": "中日友好医院",
                            "hospital_level": "三甲",
                            "id": 4052367442828932306,
                            "index": 5,
                            "indexCode": "IDXCXHSLW_DSxy9e2c_V008",
                            "levels": {
                                "L06": 4
                            },
                            "post_ts": 1671292800,
                            "site": "cndzys.com",
                            "ss": [
                                {
                                    "id": 4052367442828932306,
                                    "span": [
                                        0,
                                        303
                                    ]
                                }
                            ],
                            "summary": "糖类抗原高有可能是正常反应、妊娠、肿瘤疾...ellipses 566 char...是药物因素等原因，需要做相关检查和治疗。",
                            "title": "糖类抗原高是怎么回事",
                            "url": "https://www.cndzys.com/ylcore/art_detail/1_1092382.html"
                        }
                    ],
                    "query": "糖类抗原742增高是什么意思"
                }
            ],
            "results_source": 0,
            "routing_refuse": false
        }
    }
}
```



参数说明

<table><tr><td>参数名</td><td>含义</td><td>类型</td><td>备注</td><td>样例</td></tr><tr><td>header</td><td>头信息</td><td>Objec t</td><td></td><td></td></tr></table>

<table><tr><td>header.message</td><td>描述</td><td>String</td><td></td><td>&quot;message&quot;: &quot;success&quot;</td></tr><tr><td>header.traceld</td><td>链路ID，与请求保持一致</td><td>String</td><td>请求没有traceld，自动生成</td><td>&quot;traceld&quot;: &quot;**********&quot;</td></tr><tr><td>header.sid</td><td>客户端透传的sid</td><td>String</td><td></td><td>&quot;sid&quot;: &quot;search-flow-medical00010667@dx193b450c29db9e4401&quot;,</td></tr><tr><td>payload</td><td>返回载体</td><td>Objec</td><td>t</td><td></td></tr><tr><td>payload.routin g_refuse</td><td>是否属于医疗routing处理范围</td><td>Bool</td><td>true：不属于医疗领域，医疗团队调用其他信源兜底处理，自研不处理
false：属于医疗领域，routing服务调用自研搜索处理</td><td>&quot;routing_refuse&quot;: false</td></tr><tr><td>payload.results _source</td><td>搜索数据来源</td><td>Int</td><td>0表示自研搜索，1表示聚合搜索</td><td>&quot;results_source&quot;: &quot;0&quot;</td></tr><tr><td>payload.data</td><td>返回数据</td><td>Objec</td><td>t</td><td></td></tr><tr><td>payload.data.q uery</td><td>用户问题</td><td>String</td><td></td><td>&quot;query&quot;: &quot;牙周炎&quot;</td></tr><tr><td>payload.data.d ocs</td><td>具体文档详情</td><td>Objec</td><td>t</td><td>&quot;docs&quot;: [{}；{}；{}......{}]</td></tr><tr><td>payload.data.d ocs.index</td><td>返回结果排序位次</td><td>Int</td><td></td><td>&quot;index&quot;: 1</td></tr><tr><td>payload.data.d ocs._id</td><td>doc唯一ID</td><td>long</td><td></td><td>&quot;_id&quot;: 7515559928967085000,</td></tr><tr><td>payload.data.d ocs.id</td><td>切分文档唯一ID</td><td>long</td><td></td><td></td></tr><tr><td>payload.data.d ocs.ss</td><td>切片信息</td><td>Objec</td><td>span代表切换截断的片段，[0,514]代</td><td>&quot;ss&quot;: [ 
{&quot;id&quot;: 7515559928967085000,}</td></tr></table>

<table><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td>payload.data.d
ocs.site</td><td>站点Site</td><td>String</td><td></td><td>&quot;site&quot;: &quot;m.baidu.com&quot;</td></tr><tr><td>payload.data.d
ocs.indexCode</td><td>doc来源的
索引库编码</td><td>String</td><td></td><td>&quot;indexCode&quot;:
&quot;IDXCXHSLV_DSxy9e2c_V008&quot;</td></tr><tr><td>payload.data.d
ocs.indexName</td><td>doc来源的
索引库名称</td><td>String</td><td></td><td>&quot;indexName&quot;: &quot;医疗1130-v8(health-1180-v8)&quot;</td></tr><tr><td>payload.data.d
ocs.url</td><td>url</td><td>String</td><td></td><td>&quot;url&quot;:
&quot;https://m.baidu.com/bh/m/detail/ar_1553426616435435632&quot;</td></tr><tr><td>payload.data.d
ocs.title</td><td>标题</td><td>String</td><td></td><td>&quot;title&quot;: &quot;什么是牙周炎&quot;</td></tr><tr><td>payload.data.d
ocs.rank_score</td><td>自研搜索精
排得分</td><td>String</td><td>业务按需选择精排
或重排的得分使用
自研精排得分</td><td>&quot;rank_score&quot;: 93.33</td></tr><tr><td>payload.data.d
ocs.rerank_sco
re</td><td>自研搜索重
排得分</td><td>String</td><td>业务按需选择精排
或重排的得分使用
聚合搜索得分</td><td>&quot;rerank_score&quot;: 23.79</td></tr><tr><td>payload.data.d
ocs.summary</td><td>摘要</td><td>String</td><td>summary字段, 聚
合请求dataapi未获
取到content时使用
(预留)</td><td>&quot;summary&quot;: &quot;牙周炎是一种常见的口腔疾
病, 主要发生在牙齿与牙龈之间的牙周组织
中。通常是由于牙齿周围的......&quot;</td></tr><tr><td>payload.data.d
ocs.content</td><td>正文内容</td><td>String</td><td></td><td>&quot;content&quot;: &quot;牙周炎是一种常见的口腔疾病,
主要发生在牙齿与牙龈之间的牙周组织中。
通常是由于牙齿周围的......&quot;</td></tr><tr><td>payload.data.d
ocs.post_ts</td><td>网页发布时
间</td><td>long</td><td></td><td>&quot;post_ts&quot;: 1697040000; 单位s</td></tr><tr><td></td><td>内容Md5</td><td></td><td></td><td></td></tr></table>

<table><tr><td>payload.data.d
ocs.gid</td><td></td><td>Str
ing</td><td></td><td>&quot;gid&quot;:
&quot;9b5256244a28c74aaaba83e6e2db534f24
07df0f975baac93dcd74fe99c86520&quot;</td></tr><tr><td>payload.data.d
ocs.hospital</td><td>所属医院</td><td>Str
ing</td><td></td><td>&quot;hospital&quot;:&quot;福州爱尚贝臣口腔门诊部有限公司台江门诊部&quot;</td></tr><tr><td>payload.data.d
ocs.hospital_le
vel</td><td>医院级别</td><td>Str
ing</td><td></td><td>&quot;hospital_level&quot;:&quot;</td></tr><tr><td>payload.data.d
ocs.depart</td><td>科室</td><td>Str
ing</td><td></td><td>&quot;depart&quot;:&quot;口腔科&quot;</td></tr><tr><td>payload.data.d
ocs.doctor_lev
el</td><td>医生级别</td><td>Str
ing</td><td></td><td>&quot;doctor_level&quot;:&quot;主治医师&quot;</td></tr><tr><td>payload.data.d
ocs.doctor_na
me</td><td>医生姓名</td><td>Str
ing</td><td></td><td>&quot;doctor_name&quot;:&quot;穆素花&quot;</td></tr><tr><td>payload.data.d
ocs.doctor_ava
tar</td><td>医生照片</td><td>Str
ing</td><td></td><td>&quot;doctor_avatar&quot;:&quot;https://muzhi-public-pic.cdn.bcebos.com/100112432080&quot;</td></tr><tr><td>payload.data.d
ocs.levels</td><td>精品等级</td><td>Int</td><td></td><td>&quot;levels&quot;:{&quot;LO6&quot;:4},</td></tr><tr><td>payload.data.d
ocs.o_url</td><td>溯源url</td><td>Str
ing</td><td></td><td>&quot;o_url&quot;:
&quot;http://muzhi.baidu.com/mip_article/2000026937.html&quot;</td></tr></table>


### 2.2.2 意图分类(Intent)

自研的意图分类协议 "prodCode": "Intent"

入参



完成示例

```json
{
    "header": {
        "traceId": "61cb9e12b7a2",
        "appId":"00000000",
        "prodCode": "Intent",
        "token": "eyJhcHBJZCI6IjAwMCIsImRhdGUiOjE3MzQwNzA5MTA3ODMsInByb2RDb2RlIjoiSkJXVV8xIiwicHZpZCI6IjE4NjcxMTUxMDYyMjgzOTYwMzIiLCJyZWdpb24iOiJzaCIsInNpZ25hdHVyZSI6IjltdCtNT3RPT3kwa1U5M0Y0d3libENHVTRGUXFpYURHK2JQYWxmYkdVK0E9In0="
    },
    "parameter": {
        // 产品方案流程id
        "id": "675797478cde1539962db5f4"
    },
    "payload": {
        // 搜索业务参数
        "query": [
            "香港GDP"
        ],
        "topk": 2,
    }
}
```



参数说明  

<table><tr><td>参数名</td><td>含义</td><td>类型</td><td>是否必
传</td><td>默认值</td><td>备注</td></tr><tr><td>header</td><td>请求头</td><td>Object</td><td>是</td><td>-</td><td></td></tr><tr><td>• header.traceId</td><td>链路唯一标识</td><td>string</td><td>是</td><td>-</td><td></td></tr><tr><td>• header.appId</td><td>用户的appId信
息</td><td>string</td><td>是</td><td>-</td><td></td></tr><tr><td>• header.prodCod
e</td><td>搜索平台的产
品编码</td><td>string</td><td>是</td><td>-</td><td>与具体的搜索产品经理对接获
得</td></tr><tr><td>• header.token</td><td>路由服务返回
的token</td><td>string</td><td>是</td><td></td><td>一定需要传（会有时间有效
性）</td></tr><tr><td>• header.uid</td><td>用户级别
user_id</td><td>string</td><td>否</td><td>-</td><td>未强校验，建议长度0-25</td></tr><tr><td>• header.*</td><td>约定的其他属
性</td><td>String</td><td>否</td><td></td><td>根据具体的业务约定</td></tr><tr><td>parameter</td><td>此次回话参数
模块</td><td>Object</td><td>是</td><td>-</td><td></td></tr><tr><td>• parameter.id</td><td>产品方案流程Id</td><td>String</td><td>是</td><td>-</td><td>示例：
675797478cde1539962db5f4</td></tr><tr><td>payload</td><td>请求体</td><td>Object</td><td>是</td><td>-</td><td></td></tr></table>

<table><tr><td>·payload.query</td><td>用户问题</td><td>[string</td><td>是</td><td></td><td>【产品方案|场景策略】请求问题 0-255B
当前只处理单条query
二期支持 高级DSL</td></tr><tr><td>·payload.topk</td><td>通用领域分类返回结果数</td><td>int</td><td>是</td><td>1</td><td>通用领域分类至多25个结果，按得分排序。
通用分类包含：医疗、体育、升学、社会考试、编程、软件、历史、法律、美食、汽车、旅游、军事、娱乐、财经知识、其他财经、行业信息、前沿科技、其他教育、数码、职场技能、生活、涉政安全、古文、文学作品、其他</td></tr></table>


出参

```json
{
    "header": {
        "code": 0,
        "traceId": "b0316e5fc8b64c888b59fde37f54c432"
    },
    "payload": {
        "output": {
            "result": [
                { 
                    "query":"香港GDP",
                    "data":[
                        {
                            "label": "经济",
                            "score": 0.474
                        },
                        {
                            "label": "财经",
                            "score": 0.3
                        }
                    ]
                }
            ]
        }
    }
}
```



参数说明  

<table><tr><td>参数名</td><td>含义</td><td>类型</td><td>备注</td><td>样例</td></tr><tr><td>header</td><td>头信息</td><td>Object</td><td></td><td></td></tr><tr><td>header.code</td><td>结果状态码</td><td>Int</td><td></td><td>&quot;code&quot;:0</td></tr><tr><td>header.message</td><td>对状态码的描述</td><td>String</td><td></td><td>&quot;message&quot;:&quot;success&quot;</td></tr><tr><td>header.traceld</td><td>链路ID，与请求保持一致</td><td>String</td><td>请求没有traceld，自动生成</td><td>&quot;traceld&quot;:&quot;**********&quot;</td></tr><tr><td>header.sid</td><td>客户端透传的sid</td><td>String</td><td></td><td>&quot;sid&quot;:&quot;search-flow-medical00010667@dx193b450c29db9e4401&quot;,</td></tr><tr><td>payload</td><td>返回载体</td><td>Object</td><td></td><td></td></tr><tr><td>payload.result</td><td>返回数据</td><td>[]</td><td>result目前只会有1个元素</td><td></td></tr><tr><td>payload.result[0].data.label</td><td>对应领域</td><td>String</td><td></td><td>&quot;payload.data.label&quot;:&quot;经济&quot;</td></tr><tr><td>payload.result[0].data.score</td><td>得分</td><td>String</td><td></td><td>&quot;payload.data.score&quot;:0.474</td></tr><tr><td>payload.result[0].query</td><td>用户问题</td><td>String</td><td></td><td>&quot;query&quot;:&quot;香港GDP&quot;</td></tr></table>



## 2.3 请求示例

```json
import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import skynet.boot.auth.sdk.client.AuthHttpClient;
import skynet.boot.pandora.api.ApiResponse;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>  2024/12/12 12:09
 */
@Slf4j
public class SearchAPI {

    private static final String ROUTE_URL = "http://lynxiao-search-api-hf.xf-yun.com/v1/route";

    private final String apiKey;
    private final String apiSecret;

    public SearchAPI(String apiKey, String apiSecret) {
        this.apiKey = apiKey;
        this.apiSecret = apiSecret;
    }

    public ApiResponse route(String prodCode) throws Exception {
        try (AuthHttpClient authHttpClient = new AuthHttpClient(apiKey, apiSecret, 10000);) {
            JSONObject header = new JSONObject()
                    .fluentPut("traceId", UUID.randomUUID().toString())
                    .fluentPut("appId", apiKey)
                    .fluentPut("prodCode", prodCode);
            JSONObject payload = new JSONObject();
            JSONObject apiRequest = new JSONObject().fluentPut("header", header).fluentPut("payload", payload);
            log.info("request={}", apiRequest);
            return authHttpClient.post(ROUTE_URL, null, apiRequest, ApiResponse.class);
        }
    }

    public JSONObject search(RoutePayload routePayload, String prodCode, String query) throws Exception {
        try (AuthHttpClient authHttpClient = new AuthHttpClient(apiKey, apiSecret, 10000);) {
            String url = routePayload.getRegionUrl();
            JSONObject header = new JSONObject()
                    .fluentPut("traceId", UUID.randomUUID().toString())
                    .fluentPut("appId", apiKey)
                    .fluentPut("prodCode", prodCode)
                    .fluentPut("token", routePayload.getToken());
            // 流程id
            JSONObject parameter = new JSONObject().fluentPut("id", routePayload.getProcessId());
            SearchPayload searchPayload = new SearchPayload();
            searchPayload.setQuery(query);
            JSONObject param = new JSONObject().fluentPut("header", header).fluentPut("parameter", parameter).fluentPut("payload", searchPayload);
            log.info("search request={}", param);
            return authHttpClient.post(url, null, param, JSONObject.class);
        }
    }

    public static void main(String[] args) throws Exception {

        String apiKey = "zxvd****";
        String apiSecret = "5D2C7A0C7C4044448C*****";
        String prodCode = "xinghuo_test";

        List<String> queryList = new ArrayList<>();
        queryList.add("钢铁行业如何适应下游行业转型升级对钢铁产品的更高需求？");
        queryList.add("未来几年内，预计哪些因素将对钢铁价格和市场需求产生重大影响？");
        queryList.add("在全球贸易环境复杂多变的情况下，中国钢铁行业面临哪些挑战，以及如何应对可能对出口造成压力的贸易壁垒和技术封锁？");

        StopWatch stopWatch = StopWatch.createStarted();
        SearchAPI searchApi = new SearchAPI(apiKey, apiSecret);
        ApiResponse routeResponse = searchApi.route(prodCode);
        log.info("cost={}; routeResponse={}", stopWatch, routeResponse);
        if (routeResponse.getHeader().getCode() != 0) {
            log.error("routeResponse={}", routeResponse);
            return;
        }

        for (String query : queryList) {
            stopWatch = StopWatch.createStarted();
            RoutePayload routePayload = routeResponse.getPayload().toJavaObject(RoutePayload.class);
            JSONObject jsonObject = searchApi.search(routePayload, prodCode, query);
            log.info("cost={}; searchResponse={}", stopWatch, jsonObject);
        }
    }


    @Getter
    @Setter
    static class RoutePayload {
        /**
         * 区域编码
         */
        private String regionCode;
        /**
         * 区域地址
         */
        private String regionUrl;
        /**
         * 产品版本Id
         */
        private String pvid;
        /**
         * 产品流程Id
         */
        private String processId;
        /**
         * 授权token
         */
        private String token;
    }


    /**
     * 具体的业务 搜索请求体
     */
    @Getter
    @Setter
    static class SearchPayload {
        private String query;
        private int topk = 6;
    }
}
```



## 2.4 签名算法

代码中的apiKey就是运营提供的appId，apiSecret就是运营提供的appSecret！！！

用户向服务器申请生成一个凭证，凭证是key/secret的密钥对。客户端对Method、Accept、Date以及其他Header字段和Url进行规定方式拼接后，通过哈希算法（如HMAC- SHA256）和用户的secret对请求进行签名。最后将key、使用的算法、参与签名的头部字段以及计算后的签名放入头部字段"

请求头示例：

```shell
Content-Type:application/json
Accept:application/json,version=1.0
Date : Tue, 26 Jun 2018 12:27:03 UTC
Host:"your host"
Digest：SHA-256=xxxx
Authorization: hmac api_key="your_appId", algorithm="hmac-sha256", headers="host date request-line", signature="base64_digest"
```

签名参数 描述

Date请求日期，utc时区。格式如：Tue,26Jun201812:27:03UTC

Host请求主机，计算签名时需要该header

Digest body 的摘要，用sha256计算，计算方法为 Digest=“SHA- 256=”+base64(sha256(body))  Java语言，可以直接使用skyent- boot中的auth- sdk。详细见：[图聆知道](http://turing.iflytek.com:2230/skynet-doc/#/home?p=skynet-boot&v=4.0.15&m=skynet-boot-project%2Fskynet-boot-context%2Fsrc%2Fmain%2Fjava%2Fskynet%2Fboot%2Fsecurity%2FREADME.md&t=1734588704241#1.%20skynet.security.sign-auth%20API%E6%8E%A5%E5%8F%A3%E7%AD%BE%E5%90%8D%E9%89%B4%E6%9D%83)，

代码示例

# 3. 数据接口协议

## 3.1 字段说明

<table><tr><td>字段名</td><td>类型</td><td>是否必填</td><td>默认值</td><td>说明</td></tr><tr><td>id</td><td>Long</td><td>是</td><td></td><td>数据唯一标识</td></tr><tr><td>url</td><td>string</td><td>是</td><td></td><td>URL</td></tr><tr><td>site</td><td>string</td><td>是</td><td>&quot;</td><td>网站</td></tr><tr><td>title</td><td>string</td><td>否</td><td>&quot;</td><td>标题</td></tr><tr><td>content</td><td>string</td><td>是</td><td>&quot;</td><td>文本内容</td></tr><tr><td>post_ts</td><td>Long</td><td>否</td><td>0</td><td>发布时间 单位毫秒</td></tr><tr><td>crawl_ts</td><td>Long</td><td>否</td><td></td><td>爬取时间 单位毫秒</td></tr><tr><td>parsed_result</td><td>Map[String,Str ing]</td><td>否</td><td>{}</td><td>清洗结果字段，除 title content post_ts之外
此处有其他字段，需要与自研搜索侧线下沟通对齐</td></tr><tr><td>tags</td><td></td><td>否</td><td>{}</td><td>Doc 标签数据，例如</td></tr></table>

<table><tr><td></td><td>Map[String,Str ing]</td><td></td><td></td><td>quality_level quality_score gid 等; 此处有其他字段，需要与自研搜索侧线下沟通对齐</td></tr><tr><td>oplog</td><td>string</td><td>是</td><td></td><td>insert、update、delete</td></tr></table>

特殊说明：上述字段除了id、url、site、oplog必要字段外，当数据oplog为update，并且字段内容未变更，不传递该字段，例如交付Json中不存在post_ts字段，表明post_ts未变更，使用历史的post_ts数据。



数据文件实例：

```json
{"id":7573428185274953793,"content":"很多医生觉得惊恐障碍是一种心理问题的疾病，所以在治疗上还是选择药物和心理治疗同时进行的方法去有效的进行治疗。常见的服用的中药多是一些补脑补气血的中药比如天麻、川弓等可以有效的去进行疾病的调节和治疗。\\n在疾病的治疗上，医生会更多的建议去进行一些心理上的辅助性治疗，包括心理疗法，主要的目的就是要帮助患者用适当的方法来面对负性情感，这种治疗也是被很多的人支持的一种治疗方法;认知行为疗法，使患者能够从正确的角度去认识自己在生活中看到或者是经历的一些场景，从而去改变自己的一些行为措施，帮助患者不断地采取各种方式来缓解由此带来的不适应，缓解惊恐的出现;精神动力学疗法，这种方法就是帮助患者去正确的解决自己生活中的一些某些情绪或思维的冲突，有效的避免惊恐障碍的出现。\\n要想使自己的恐惧障碍得到有效的提高和客服，必须从自己的内心去坚定思想，能够有效的去训练自己对各种事情的平常心态，更好的做到在平时的生活中保持良好的沉着冷静的为人处世的方法，做到不惊慌失措，不过度恐慌。\\n中药在很大程度上都是对患者的身体以及脑部所缺物质的补充，但是在很大程度上疾病的治疗还是需要我们去选择正确的有效的治疗方法才能从根本上缓解和治疗疾病，但是必要的药物选择还是需要做到有效的了解，才能更为准确的去进行治疗。最后，患有惊恐障碍的病人需要对症治疗，越早治疗效果会越好一些，不可以耽误治疗的时机，也不可以有侥幸心理，需要严格的进行药物控制或者是心理治疗，只要坚持治疗一段时间，病情的改善就会比较明显一些","url":"https://tag.120ask.com/jibing/jkza/673904.html","title":"中医能治好惊恐障碍","site":"120ask.com","post_ts":1671638400000,"crawl_ts":1722967200000,"parsed_result":{"author":"","doctor_label":"","source":"","hospital_level":"","doctor_position":"","hospital":"中国医学科学院北京协和医院","doctor_name":"尹德海","author_url":"","depart":"中医综合科","author_id":"","summary":"","doctor_level":"主任医师"},"tags":{"quality_level":"A","quality_score":"0.9828097820281982","gid":"ea585f897f98f2697fbfb9878af7f5e540ee1fcf76a945b4121de8c940e3064c"},"oplog":"insert"}
{"id":7573428185274953793,"content":"很多医生觉得惊恐障碍是一种心理问题的疾病，所以在治疗上还是选择药物和心理治疗同时进行的方法去有效的进行治疗。常见的服用的中药多是一些补脑补气血的中药比如天麻、川弓等可以有效的去进行疾病的调节和治疗。\\n在疾病的治疗上，医生会更多的建议去进行一些心理上的辅助性治疗，包括心理疗法，主要的目的就是要帮助患者用适当的方法来面对负性情感，这种治疗也是被很多的人支持的一种治疗方法;认知行为疗法，使患者能够从正确的角度去认识自己在生活中看到或者是经历的一些场景，从而去改变自己的一些行为措施，帮助患者不断地采取各种方式来缓解由此带来的不适应，缓解惊恐的出现;精神动力学疗法，这种方法就是帮助患者去正确的解决自己生活中的一些某些情绪或思维的冲突，有效的避免惊恐障碍的出现。\\n要想使自己的恐惧障碍得到有效的提高和客服，必须从自己的内心去坚定思想，能够有效的去训练自己对各种事情的平常心态，更好的做到在平时的生活中保持良好的沉着冷静的为人处世的方法，做到不惊慌失措，不过度恐慌。\\n中药在很大程度上都是对患者的身体以及脑部所缺物质的补充，但是在很大程度上疾病的治疗还是需要我们去选择正确的有效的治疗方法才能从根本上缓解和治疗疾病，但是必要的药物选择还是需要做到有效的了解，才能更为准确的去进行治疗。最后，患有惊恐障碍的病人需要对症治疗，越早治疗效果会越好一些，不可以耽误治疗的时机，也不可以有侥幸心理，需要严格的进行药物控制或者是心理治疗，只要坚持治疗一段时间，病情的改善就会比较明显一些","url":"https://tag.120ask.com/jibing/jkza/673904.html","title":"中医能治好惊恐障碍","site":"120ask.com","crawl_ts":1722967200000,"tags":{"quality_level":"A","quality_score":"0.9828097820281982","gid":"ea585f897f98f2697fbfb9878af7f5e540ee1fcf76a945b4121de8c940e3064c"},"oplog":"update"}
{"id":7573428185274953793,"url":"https://tag.120ask.com/jibing/jkza/673904.html","site":"120ask.com","oplog":"delete"}
```



## 3.2 数据路径

通过`kafka`消息队列进行数据投递

topic：不同业务方不同 topic

	- topic命名约定：`lynxiao_3rd_data_\{业务方编码\}`
	- 如xiaozhi `lynxiao_3rd_data_xiaozhi`

备注：临时联调 Kafka 地址：10.103.254.28:9093,10.103.254.29:9093,10.103.254.30:9093 正式接入地址待定。（搜索团队搭建中），元旦后。



## 3.3 示例数据

### 3.3.1 新增示例数据

```json
{
    "id": 7573428185274953793,
    "content": "很多医生觉得惊恐障碍是一种心理问题的疾病，所以在治疗上还是选择药物和心理治疗同时进行的方法去有效的进行治疗。常见的服用的中药多是一些补脑补气血的中药比如天麻、川弓等可以有效的去进行疾病的调节和治疗。\\n在疾病的治疗上，医生会更多的建议去进行一些心理上的辅助性治疗，包括心理疗法，主要的目的就是要帮助患者用适当的方法来面对负性情感，这种治疗也是被很多的人支持的一种治疗方法;认知行为疗法，使患者能够从正确的角度去认识自己在生活中看到或者是经历的一些场景，从而去改变自己的一些行为措施，帮助患者不断地采取各种方式来缓解由此带来的不适应，缓解惊恐的出现;精神动力学疗法，这种方法就是帮助患者去正确的解决自己生活中的一些某些情绪或思维的冲突，有效的避免惊恐障碍的出现。\\n要想使自己的恐惧障碍得到有效的提高和客服，必须从自己的内心去坚定思想，能够有效的去训练自己对各种事情的平常心态，更好的做到在平时的生活中保持良好的沉着冷静的为人处世的方法，做到不惊慌失措，不过度恐慌。\\n中药在很大程度上都是对患者的身体以及脑部所缺物质的补充，但是在很大程度上疾病的治疗还是需要我们去选择正确的有效的治疗方法才能从根本上缓解和治疗疾病，但是必要的药物选择还是需要做到有效的了解，才能更为准确的去进行治疗。最后，患有惊恐障碍的病人需要对症治疗，越早治疗效果会越好一些，不可以耽误治疗的时机，也不可以有侥幸心理，需要严格的进行药物控制或者是心理治疗，只要坚持治疗一段时间，病情的改善就会比较明显一些",
    "url": "https://tag.120ask.com/jibing/jkza/673904.html",
    "title": "中医能治好惊恐障碍",
    "site": "120ask.com",
    "post_ts": 1671638400000,
    "crawl_ts": 1722967200000,
    "parsed_result":
    {
        "author": "",
        "doctor_label": "",
        "source": "",
        "hospital_level": "",
        "doctor_position": "",
        "hospital": "中国医学科学院北京协和医院",
        "doctor_name": "尹德海",
        "author_url": "",
        "depart": "中医综合科",
        "author_id": "",
        "summary": "",
        "doctor_level": "主任医师"
    },
    "tags":
    {
        "quality_level": "A",
        "quality_score": "0.9828097820281982",
        "gid": "ea585f897f98f2697fbfb9878af7f5e540ee1fcf76a945b4121de8c940e3064c"
    }，
    "oplog": "insert"
}
```



### 3.3.2 修改示例数据

```json

{
    "id": 7573428185274953793,
    "content": "很多医生觉得惊恐障碍是一种心理问题的疾病，所以在治疗上还是选择药物和心理治疗同时进行的方法去有效的进行治疗。常见的服用的中药多是一些补脑补气血的中药比如天麻、川弓等可以有效的去进行疾病的调节和治疗。\\n在疾病的治疗上，医生会更多的建议去进行一些心理上的辅助性治疗，包括心理疗法，主要的目的就是要帮助患者用适当的方法来面对负性情感，这种治疗也是被很多的人支持的一种治疗方法;认知行为疗法，使患者能够从正确的角度去认识自己在生活中看到或者是经历的一些场景，从而去改变自己的一些行为措施，帮助患者不断地采取各种方式来缓解由此带来的不适应，缓解惊恐的出现;精神动力学疗法，这种方法就是帮助患者去正确的解决自己生活中的一些某些情绪或思维的冲突，有效的避免惊恐障碍的出现。\\n要想使自己的恐惧障碍得到有效的提高和客服，必须从自己的内心去坚定思想，能够有效的去训练自己对各种事情的平常心态，更好的做到在平时的生活中保持良好的沉着冷静的为人处世的方法，做到不惊慌失措，不过度恐慌。\\n中药在很大程度上都是对患者的身体以及脑部所缺物质的补充，但是在很大程度上疾病的治疗还是需要我们去选择正确的有效的治疗方法才能从根本上缓解和治疗疾病，但是必要的药物选择还是需要做到有效的了解，才能更为准确的去进行治疗。最后，患有惊恐障碍的病人需要对症治疗，越早治疗效果会越好一些，不可以耽误治疗的时机，也不可以有侥幸心理，需要严格的进行药物控制或者是心理治疗，只要坚持治疗一段时间，病情的改善就会比较明显一些",
    "url": "https://tag.120ask.com/jibing/jkza/673904.html",
    "title": "中医能治好惊恐障碍",
    "site": "120ask.com",
    "crawl_ts": 1722967200000,
    "tags":
    {
        "quality_level": "A",
        "quality_score": "0.9828097820281982",
        "gid": "ea585f897f98f2697fbfb9878af7f5e540ee1fcf76a945b4121de8c940e3064c"
    }，
    "oplog": "update"
}
```



### 3.3.3 删除示例数据

```json
{
    "id": 7573428185274953793,
    "url": "https://tag.120ask.com/jibing/jkza/673904.html",
    "site": "120ask.com",
    "oplog": "delete"
}
```



# 4. 附录

## 4.1 GoLang-SDK:

[搜索接口- GoLang- SDK](https://yf2ljykclb.xfchat.iflytek.com/wiki/SlXbwTiHTimJYxkCtUsrpuNZzHh)



## 4.2 签名生成代码示例

### Java

```java
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * 鉴权工具类
 *
 */
public class AuthUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(AuthUtils.class);


    public static final String SKYNET_AUTH_ERR_MESSAGE = "SKYNET_AUTH_ERR_MESSAGE";

    public static final Charset CHARSET_UTF8 = StandardCharsets.UTF_8;
    public static final String ALGORITHM_NAME = "hmacsha256";
    public static final String DIGEST_NAME = "SHA-256";
    public static final String AUTH_KEY = "Authorization";
    public static final String DATE_FORMAT_PATTERN = "EEE, dd MMM yyyy HH:mm:ss z";

    public static String assembleRequestUrl(String requestUrl, String apiKey, String apiSecret) {
        return assembleRequestUrl(HttpMethod.GET, requestUrl, apiKey, apiSecret);
    }
    
    public static String assemblePostRequestUrl(String requestUrl, String apiKey, String apiSecret) {
        return assembleRequestUrl(HttpMethod.POST, requestUrl, apiKey, apiSecret);
    }

    public static String assembleRequestUrl(HttpMethod httpMethod, String requestUrl, String apiKey, String apiSecret) {
        return assembleRequestUrl(httpMethod, requestUrl, apiKey, apiSecret, null);
    }

    public static String assembleRequestUrl(HttpMethod httpMethod, String requestUrl, String apiKey, String apiSecret, String apiKeyKey) {
        AuthorizationData authorizationData = assemble(httpMethod, requestUrl, apiKey, apiSecret, null, apiKeyKey);
        try {
            String authBase = Base64.getEncoder().encodeToString(authorizationData.getAuthorization().getBytes(CHARSET_UTF8));
            authBase = String.format("%s?%s=%s&host=%s&date=%s", requestUrl, AUTH_KEY.toLowerCase(), URLEncoder.encode(authBase, "utf-8"),
                    URLEncoder.encode(authorizationData.getHost(), "utf-8"), URLEncoder.encode(authorizationData.getDate(), "utf-8"));
            LOGGER.debug("assembleRequestUrl:{}", authBase);
            return authBase;
        } catch (Exception e) {
            LOGGER.error("assemble RequestUrl error ", e);
            throw new RuntimeException(e);
        }
    }

    public static Map<String, String> assembleAuthorizationHeaders(String requestUrl, String apiKey, String apiSecret) {
        return assembleAuthorizationHeaders(HttpMethod.GET, requestUrl, apiKey, apiSecret, null, null);
    }
 
    public static Map<String, String> assembleAuthorizationHeaders(HttpMethod httpMethod, String requestUrl, String apiKey, String apiSecret, String body) {
        return assembleAuthorizationHeaders(httpMethod, requestUrl, apiKey, apiSecret, body, null);
    }

    public static Map<String, String> assembleAuthorizationHeaders(HttpMethod httpMethod, String requestUrl, String apiKey, String apiSecret, String body, String apiKeyKey) {
        AuthorizationData authorizationData = assemble(httpMethod, requestUrl, apiKey, apiSecret, body, apiKeyKey);
        return authorizationData.getHeader();
    }


    private static AuthorizationData assemble(HttpMethod httpMethod, String requestUrl, String apiKey, String apiSecret, String body, String apiKeyKey) {
        // 校验参数
        URL url;
        String httpRequestUrl = requestUrl.replace("ws://", "http://").replace("wss://", "https://");
        try {
            url = new URL(httpRequestUrl);
            SimpleDateFormat format = new SimpleDateFormat(DATE_FORMAT_PATTERN, Locale.US);
            format.setTimeZone(TimeZone.getTimeZone("GMT"));
            String date = format.format(new Date());

            String sha = getSignature(url.getHost(), date, getRequestLine(httpMethod.toString(), url.getPath()), apiSecret);

            String digest = null;
            if (StringUtils.isNotBlank(body)) {
                digest = signBody(body);
            }
            apiKeyKey = StringUtils.isBlank(apiKeyKey) ? "hmac api_key" : apiKeyKey;
            String authorization = String.format("%s=\"%s\", algorithm=\"hmac-sha256\", headers=\"host date request-line%s\", signature=\"%s\"", apiKeyKey, apiKey, StringUtils.isBlank(digest) ? "" : " digest", sha);
            AuthorizationData authorizationData = new AuthorizationData();
            authorizationData.setDate(date).setHost(url.getHost()).setAuthorization(authorization).setDigest(digest);
            LOGGER.debug("authorizationData:\t[{}]", authorizationData);
            return authorizationData;
        } catch (Exception e) {
            LOGGER.error("assemble AuthorizationData error", e);
            throw new RuntimeException(e);
        }
    }

    public static String getRequestLine(String method, String path) {
        return String.format("%s %s HTTP/1.1", method.toUpperCase(), path);
    }


    public static String getSignature(String host, String date, String requestLine, String apiSecret) {
        // 校验参数
        try {
            URI uri = new URI("skynet://" + host);
            StringBuilder builder = new StringBuilder("host: ").append(uri.getHost()).append("\n").
                    append("date: ").append(date).append("\n");
            builder.append(requestLine);

            LOGGER.debug("\n--signing string:---------------------------------------\n{}\n--signing string:---------------------------------------", builder);

            Mac mac = Mac.getInstance(ALGORITHM_NAME);

            SecretKeySpec spec = new SecretKeySpec(apiSecret.getBytes(CHARSET_UTF8), ALGORITHM_NAME);
            mac.init(spec);

            byte[] hexDigits = mac.doFinal(builder.toString().getBytes(CHARSET_UTF8));
            String signature = Base64.getEncoder().encodeToString(hexDigits);

            LOGGER.debug("signature:{}", signature);
            return signature;
        } catch (Exception e) {
            LOGGER.error("assemble AuthorizationData error", e);
            throw new RuntimeException(e);
        }
    }

    public static String signBody(String body) throws Exception {
        if (StringUtils.isBlank(body)) {
            throw new IllegalArgumentException("body is empty.");
        }
        try {
            return signBody(body.getBytes(StandardCharsets.UTF_8));
        } catch (UnsupportedEncodingException e) {
            LOGGER.error("Body签名失败：{}", e.getMessage());
        }
        return null;
    }

    public static String signBody(byte[] body) throws Exception {
        if (body == null || body.length == 0) {
            throw new IllegalArgumentException("body is empty.");
        }
        MessageDigest messageDigest;
        String digest = "";
        try {
            messageDigest = MessageDigest.getInstance(DIGEST_NAME);
            messageDigest.update(body);
            digest = Base64.getEncoder().encodeToString(messageDigest.digest());
        } catch (NoSuchAlgorithmException e) {
            LOGGER.error("Body签名失败：{}", e.getMessage());
        }
        return digest;
    }


    static class AuthorizationData {
        private String date;
        private String host;
        private String authorization;
        private String digest;

        public Map<String, String> getHeader() {
            Map<String, String> headers = new LinkedHashMap<>(4);

            headers.put("Host", host);
            headers.put("Date", date);
            if (StringUtils.isNotBlank(digest)) {
                headers.put("Digest", String.format("%s=%s", DIGEST_NAME, digest));
            }
            headers.put(AUTH_KEY, authorization);
            return headers;
        }
        // getter setter
        @Override
        public String toString() {
            return String.format("host=%s;date=%s;digest=%s;authorization=%s;", this.host, this.date, this.digest, this.authorization);
        }
    }
}
```



### Python

```python
import time
from datetime import datetime
from wsgiref.handlers import format_date_time
from time import mktime
import hashlib
import base64
import hmac
from urllib.parse import urlparse

def assemble_auth_header(url, method, api_key, api_secret):
    u = urlparse(url)
    host = u.hostname
    path = u.path
    now = datetime.now()
    date = format_date_time(mktime(now.timetuple()))
    signature_origin = "host: {}\ndate: {}\n{} {} HTTP/1.1".format(host, date, method, path)
    signature_sha = hmac.new(api_secret.encode('utf-8'), signature_origin.encode('utf-8'),
                             digestmod=hashlib.sha256).digest()
    signature_sha = base64.b64encode(signature_sha).decode(encoding='utf-8')
    authorization = "hmac api_key=\"%s\", algorithm=\"%s\", headers=\"%s\", signature=\"%s\"" % (
        api_key, "hmac-sha256", "host date request-line", signature_sha)
    headers = {
        "host": host,
        "date": date,
        "authorization": authorization,
    }
    return headers
```



### Go

```go
import (
    "crypto/hmac"
    "crypto/sha256"
    "encoding/base64"
    "fmt"
    "net/url"
    "time"
)

func AssembleAuthUrl(requestUrl, method, apiKey, apiSecret string) (string, error) {
    urlResult, err := url.Parse(requestUrl)
    if err != nil {
        return "", err
    }
    date := time.Now().UTC().Format(time.RFC1123)

    // 创建签名原始字符串
    signatureOrigin := fmt.Sprintf("host: %s\ndate: %s\n%s %s HTTP/1.1",
        urlResult.Hostname(), date, method, urlResult.Path)

    // 计算HMAC SHA-256签名
    h := hmac.New(sha256.New, []byte(apiSecret))
    h.Write([]byte(signatureOrigin))
    signatureSHA := h.Sum(nil)
    signature := base64.StdEncoding.EncodeToString(signatureSHA)

    // 生成授权头
    authorizationOrigin := fmt.Sprintf("hmac api_key=\"%s\", algorithm=\"%s\", headers=\"%s\", signature=\"%s\"",
        apiKey, "hmac-sha256", "host date request-line", signature)
    authorization := base64.StdEncoding.EncodeToString([]byte(authorizationOrigin))

    values := url.Values{}
    values.Add("host", urlResult.Hostname())
    values.Add("date", date)
    values.Add("authorization", authorization)

    return fmt.Sprintf("%s?%s", requestUrl, values.Encode()), nil

}
```

